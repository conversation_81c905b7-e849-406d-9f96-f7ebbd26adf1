[{"id": "673e1dfdfe6cc102e976f7b0", "m_name": "Activate6StonesQuest", "m_type": "Activate6StonesQuest", "m_explainText": "Find the Hippy", "m_targetValue": "0.00", "m_power": "Quest6Stones", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67f3efa4063aa702d1dd9c25", "m_name": "ActivateBriarLakesBeacon", "m_type": "ActivateBeacon", "m_explainText": "Activate the Briar Lakes beacon\n", "m_targetValue": "0.00", "m_power": "BriarLakeBeacon", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67d0c4589cc40a02fe44c777", "m_name": "ActivateOakridgeBeacon", "m_type": "ActivateBeacon", "m_explainText": "Approach the Oakridge beacon as the Hero and activate it.", "m_targetValue": "0.00", "m_power": "RoadToWyrmscarBeacon", "m_repeatCount": 1, "m_execute": "", "m_advisor": ""}, {"id": "67d44c270667a4030aace866", "m_name": "ActivateWyrmscarBeacon", "m_type": "ActivateBeacon", "m_explainText": "Activate the Wrymscar beacon\n<size=80%>Tip: It's near the coast</size>", "m_targetValue": "0.00", "m_power": "WyrmscarBeacon", "m_repeatCount": 1, "m_execute": "", "m_advisor": ""}, {"id": "615701166e19d40020c74d82", "m_name": "AdvEarnMoney", "m_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_explainText": "Earn [Value] from product sales", "m_targetValue": "50.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6102ba8668a1f6001efd9e70", "m_name": "BasicEarnMoney", "m_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_explainText": "Earn [Value] more from product sales.", "m_targetValue": "25.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "68552ac908b3ce02f22b43e5", "m_name": "BuildArmourerBriarLake", "m_type": "RebuildBuilding", "m_explainText": "Complete the Armourer in Briar Lake", "m_targetValue": "1.00", "m_power": "Building[429]\nMA_Armourer_RoofSide\nMA_Armourer_Window\nMA_Armourer_Shade\nMA_Armourer_Roof_Chimney", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67ed5b6f47b81802be7e630b", "m_name": "BuildBriarLakeFactory", "m_type": "RebuildBuilding", "m_explainText": "Build a new Factory.", "m_targetValue": "1.00", "m_power": "Building[LastBuilt]\nStockIn\nMA_Factory_Action\nEntrance\nStockOut\nChimney\nGUIOrderInfo", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "659c242c026bdd002614e72f", "m_name": "BuildCottersHouse", "m_type": "BuildCottersHouse", "m_explainText": "Build The Cotters's Mediocre Dream House", "m_targetValue": "0.50", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67ed2b27b709b102c5189d35", "m_name": "BuildDispatchBriarLake", "m_type": "RebuildBuilding", "m_explainText": "Build a new Dispatch.", "m_targetValue": "1.00", "m_power": "Building[407]\nEntrance\nMA_OrderBoard_Cotton\nStockIn", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6724f3b1eb58bf02a3bafc78", "m_name": "BuildHeroGuild", "m_type": "RebuildBuilding", "m_explainText": "Build the Heroes' Guild", "m_targetValue": "1.00", "m_power": "Building[LastBuilt]\nMA_HeroesGuild_Door\nGuildBedroom\nRoof\nMA_HeroesGuild_Training", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67ba43a171c9e50f7d06e3be", "m_name": "BuildLumberMill", "m_type": "RebuildBuilding", "m_explainText": "Build the Lumber Mill", "m_targetValue": "1.00", "m_power": "Building[LastBuilt]\nActionLumberMill\nEntrance\nAesthetic\nStockIn\nStockOut", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6578448a70870d002a5f9534", "m_name": "BuildMetalMine", "m_type": "RebuildBuilding", "m_explainText": "Build the Metal Mine ", "m_targetValue": "1.00", "m_power": "Building[424]\nMA_MetalMine_Action\nEntrance\nAesthetic\nRoof\nStockOut\n", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "66fc25041e64a902ec1a0d89", "m_name": "BuildThreeWorkerHouses", "m_type": "Build", "m_explainText": "Build [Value] [Power]", "m_targetValue": "3.00", "m_power": "ActionHouse", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67ba32f56376290ca2a28350", "m_name": "BuildTurret", "m_type": "RebuildBuilding", "m_explainText": "Build your first Turret", "m_targetValue": "1.00", "m_power": "Building[LastBuilt]\nMA_Turret_Structure\nMA_Turret_Stacker\nMA_Turret_Stacker\nActionTurret\nAmmoStockIn", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67a4d5f795531d32354b1c50", "m_name": "BuildWall", "m_type": "BuildWall", "m_explainText": "Complete Wall by Dragging Wood\n<size=80%>[Value]% complete.</size>", "m_targetValue": "20.00", "m_power": "Pos[-105;30]", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67ceef9ff1f58b0336bb2dc0", "m_name": "BuildWeaponsmith", "m_type": "RebuildBuilding", "m_explainText": "Build the Weaponsmith", "m_targetValue": "1.00", "m_power": "Building[LastBuilt]\nMA_Weaponsmith_Roof_Chimney\nMA_Weaponsmith_Door\nActionWeaponsmith\nStockIn\n", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67dc22489c9e3a03185b32c0", "m_name": "CaveSwordOrder", "m_type": "CompleteCurrentOrder", "m_explainText": "Deliver the sword to <PERSON> so he can enter the cave.", "m_targetValue": "1.00", "m_power": "QuestSwordRack", "m_repeatCount": 0, "m_execute": "", "m_advisor": ""}, {"id": "67213bd2c6a74102c722e9a7", "m_name": "CleanDispatch", "m_type": "WaitForBuildingToBeClean", "m_explainText": "Clean the Dispatch", "m_targetValue": "1.00", "m_power": "Building[50]", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67213b9dc6a74102c722e812", "m_name": "CleanHouse", "m_type": "WaitForBuildingToBeClean", "m_explainText": "Clean the House", "m_targetValue": "1.00", "m_power": "Building[44]", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67213b32c6a74102c722e712", "m_name": "CleanTavern", "m_type": "WaitForBuildingToBeClean", "m_explainText": "Clean the Tavern", "m_targetValue": "1.00", "m_power": "Building[49]", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "659d79bb22e88b0029a5678c", "m_name": "ClickOnRoyalOrder", "m_type": "ClickOnOrder", "m_explainText": "Click the Royal Order on the Order Board", "m_targetValue": "1.00", "m_power": "Royal Council", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "65ed9d68d1a3bb0026ea18df", "m_name": "CollectProduceItems", "m_type": "CollectItems", "m_explainText": "Collect [Value] Items", "m_targetValue": "1.00", "m_power": "Flour,2\nWheat,3", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "689dff21b8556802eb327485", "m_name": "CompleteEckoBeachBeacon", "m_type": "CompleteBeacon", "m_explainText": "Construct the beacon and charge it with manna.\n<size=80%>Tap and hold to charge the beacon.</size>", "m_targetValue": "0.00", "m_power": "EckoBeachBeacon", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67ee5c7d17ce7d03131b65e6", "m_name": "CompleteOakridgeBeacon", "m_type": "CompleteBeacon", "m_explainText": "Construct the beacon and charge it with manna.\n<size=80%>Tap and hold to charge the beacon.</size>", "m_targetValue": "0.00", "m_power": "RoadToWyrmscarBeacon", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67c6f9583e81f902d9d8b8c3", "m_name": "CompleteTrickyOrder", "m_type": "CompleteOrder", "m_explainText": "Complete Tricky's  order\n<size=80%>[Value] Left.</size>", "m_targetValue": "1.00", "m_power": "special:0100", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "683ee3b03fd1d602eb64d87f", "m_name": "CompleteValmeyInterlude", "m_type": "QuestComplete", "m_explainText": "Chaperone your Aunty Valmster", "m_targetValue": "1.00", "m_power": "MAQuestValmeysInterlude", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "681344943df59f02ffb1709f", "m_name": "CompleteWyrmscarBeacon", "m_type": "CompleteBeacon", "m_explainText": "Construct the Wyrmscar beacon and charge it with manna.\n<size=80%>Tap and hold to charge the beacon.</size>", "m_targetValue": "0.00", "m_power": "WyrmscarBeacon", "m_repeatCount": 1, "m_execute": "", "m_advisor": ""}, {"id": "675ac5274431e802fb38e9c3", "m_name": "CotterAddWorkerBlocks", "m_type": "CotterAddWorkerBlocks", "m_explainText": "Add worker blocks To:\\n", "m_targetValue": "0.00", "m_power": "Farm+1;Mill+1;Factory+1", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6735dd73eb54f302e7c30bda", "m_name": "CreateCottersMeals", "m_type": "CompleteCurrentOrder", "m_explainText": "Create Cotters Meals\n<size=80%>[Value] more to go.</size>", "m_targetValue": "0.00", "m_power": "QuestTable", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "683d92c60d5bc3030ef1f4e1", "m_name": "CreateGarrickCoat", "m_type": "CompleteCurrentOrder", "m_explainText": "Make me a unique, stylish outfit.\nSee my tailor in Briar Lake Tavern.", "m_targetValue": "0.00", "m_power": "BriarLakeQuestTable", "m_repeatCount": 0, "m_execute": "", "m_advisor": ""}, {"id": "6859632eb912ca17b2ba81ae", "m_name": "CreateValmeyDress", "m_type": "CompleteCurrentOrder", "m_explainText": "Design and Produce a dress fit for a <PERSON><PERSON><PERSON>.", "m_targetValue": "0.00", "m_power": "BriarLakeQuestTable", "m_repeatCount": 0, "m_execute": "", "m_advisor": ""}, {"id": "689dff26a0a68c0303f1365f", "m_name": "DeleteMe", "m_type": "", "m_explainText": "", "m_targetValue": "0.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "61389c6d56c2e5001e241a4b", "m_name": "DesignProduct", "m_type": "DesignProduct", "m_explainText": "Design [Value] Product(s)", "m_targetValue": "1.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "65a94a1c25293f002763f529", "m_name": "DesignRoyalSword", "m_type": "DesignProduct", "m_explainText": "Design [Value] <color=orange>weak</color> sword(s) fit for the <PERSON>'s men", "m_targetValue": "1.00", "m_power": "Special:0010", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6578358b72057f0027ee00f7", "m_name": "DesignSword", "m_type": "DesignProduct", "m_explainText": "Design [Value] sword to a [Quality] quality", "m_targetValue": "1.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "673f1d93e7af1d02d45d493a", "m_name": "DesignWall", "m_type": "DesignWall", "m_explainText": "Build a [TargetValue]m wall to stop the attacks\n<size=80%>[Value]m left.</size>", "m_targetValue": "20.00", "m_power": "Pos[-124;77]", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "65e9eda55e067600271f9fb7", "m_name": "<PERSON><PERSON>y<PERSON><PERSON><PERSON><PERSON>", "m_type": "<PERSON><PERSON>y<PERSON><PERSON><PERSON><PERSON>", "m_explainText": "Destroy the obstruction on the metal mine base", "m_targetValue": "0.00", "m_power": "Building[102]", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67ed42f809df6f02f870a5d7", "m_name": "District9WorkerCount6", "m_type": "TotalWorkers", "m_explainText": "Have [TargetValue] workers employed in Briar Lakes\n<size=80%>[Value] more to hire.</size>", "m_targetValue": "6.00", "m_power": "district9", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "615f0227c97256001e259e43", "m_name": "EarnMoneyFromSales1", "m_type": "EarnMoneyFromSales", "m_explainText": "Earn [Value] from product sales", "m_targetValue": "50.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "615f0237d89bd7001ebac95f", "m_name": "EarnMoneyFromSales2", "m_type": "EarnMoneyFromSales", "m_explainText": "Earn [Value] from product sales", "m_targetValue": "100.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "616035e15b32d6001f614c1a", "m_name": "EarnMoneyFromSales3", "m_type": "EarnMoneyFromSales", "m_explainText": "Earn [Value] from product sales", "m_targetValue": "150.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "61fbd0f3b5b7dd001e1a0c26", "m_name": "EarnMoneyFromSales4", "m_type": "EarnMoneyFromSales", "m_explainText": "Earn [Value] from product sales", "m_targetValue": "250.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "61fbd127f704c0001ee285c6", "m_name": "EarnMoneyFromSales5", "m_type": "EarnMoneyFromSales", "m_explainText": "Earn [Value] from product sales", "m_targetValue": "500.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "61fbd15254d1f6001e30f6ae", "m_name": "EarnMoneyFromSales6", "m_type": "EarnMoneyFromSales", "m_explainText": "Earn [Value] from product sales", "m_targetValue": "750.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "61fbd16482fa1e001e6ce8c9", "m_name": "EarnMoneyFromSales7", "m_type": "EarnMoneyFromSales", "m_explainText": "Earn [Value] from product sales", "m_targetValue": "1000.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "622f3f7a4d0a50001feb7a58", "m_name": "EarnMoneyFromSalesBase", "m_type": "EarnMoneyFromSales", "m_explainText": "Earn [Value] from product sales", "m_targetValue": "100.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "62cd53c451b5e900217ddec0", "m_name": "EarnMoneyFromSalesBase_duplicate", "m_type": "EarnMoneyFromSales", "m_explainText": "Earn [Value] from product sales", "m_targetValue": "100.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "616d70cc239dd1001e791706", "m_name": "EmployMiners", "m_type": "EmployMiners", "m_explainText": "Employ [Value] Miners", "m_targetValue": "1.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "657847c56945fc00288e9c3e", "m_name": "EmployWorker", "m_type": "TotalWorkers", "m_explainText": "Employ [Value] Worker", "m_targetValue": "1.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "66fc1d19e431c1031c7ae060", "m_name": "EmployWorkerInFactory", "m_type": "TotalWorkers", "m_explainText": "Hire [Value] Worker in the Factory", "m_targetValue": "1.00", "m_power": "Building[56]", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "66fc1be0bcbe5402c0bdad71", "m_name": "EmployWorkerInFarm", "m_type": "TotalWorkers", "m_explainText": "Hire [Value] Worker in the Farm", "m_targetValue": "1.00", "m_power": "Building[101]", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "657847b070870d002a5fafcc", "m_name": "EmployWorkerInMetalMine", "m_type": "TotalWorkers", "m_explainText": "Employ [Value] Workers in the Metal Mine", "m_targetValue": "2.00", "m_power": "Building[103]", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "657847bb6945fc00288e9beb", "m_name": "EmployWorkerInMetalSmelter", "m_type": "TotalWorkers", "m_explainText": "Employ [Value] Worker in the Metal Smelter", "m_targetValue": "1.00", "m_power": "Building[MetalSmelter]", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "66fc1cb1ead3a202e3886e85", "m_name": "EmployWorkerInMill", "m_type": "TotalWorkers", "m_explainText": "Employ [Value] Worker in the Mill", "m_targetValue": "1.00", "m_power": "Building[209]", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "68395feb4d8a6e031c961999", "m_name": "EnterCyrstalCave", "m_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_explainText": "Return to Ecko Beach and enter the Crystal Caves", "m_targetValue": "0.00", "m_power": "WaitForCaveEnter(Cave_Tunnel_Start_EnterA)", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6839615675754f02e02813f1", "m_name": "ExitCyrstalCave", "m_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_explainText": "Find your path through the Crystal Caves", "m_targetValue": "0.00", "m_power": "WaitForCaveExit(Cave_Tunnel_Start_EnterB)", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "680931614248bb02b0a0f74f", "m_name": "ExitWyrmscarCave", "m_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_explainText": "Possess your hero and make your way through Wyrmscar Cave", "m_targetValue": "0.00", "m_power": "WaitForCaveExit(Cave_MineShaft_A_EnterB)", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "659c20be08e218002731f16e", "m_name": "FindCottersHouseLocation", "m_type": "FindCottersHouseLocation", "m_explainText": "Find the right spot for their new home", "m_targetValue": "1.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67f2f18cb2b05a03045bcf61", "m_name": "<PERSON><PERSON><PERSON><PERSON>", "m_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_explainText": "Seek G<PERSON> at Bandit Camp Tavern.", "m_targetValue": "0.00", "m_power": "WaitForQuestActivate(FlowCharacter[<PERSON><PERSON><PERSON>], 4,Possessed)", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67fad81382179a03088baf66", "m_name": "FindMineWorker", "m_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_explainText": "Seek out the cave entrance and the mine worker", "m_targetValue": "0.00", "m_power": "WaitForQuestActivate(FlowCharacter[DougMineWorker], 4)", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "68665f5162b0670316783491", "m_name": "FindValmeyBanditCamp", "m_type": "PossessedInRange", "m_explainText": "Find <PERSON>. \\n\nLast seen on a warpath to the Bandit King…", "m_targetValue": "15.00", "m_power": "FlowCharacter[<PERSON><PERSON><PERSON>]", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67f931c2666654578786dfd7", "m_name": "FindValmeyInterlude", "m_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_explainText": "Seek out <PERSON><PERSON><PERSON> in Oakridge", "m_targetValue": "0.00", "m_power": "WaitForQuestActivate(FlowCharacter[Valmey], 4)", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6899c0246d565002ee4342a4", "m_name": "GiftRecivedDairyPack", "m_type": "GiftRecived", "m_explainText": "Drag out [Value] Card.", "m_targetValue": "1.00", "m_power": "MA_RL_DairyPack", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67f843708f292802e95b2041", "m_name": "GrimshawInspection", "m_type": "GrimshawInspection", "m_explainText": "Take <PERSON><PERSON><PERSON>'s Inspection", "m_targetValue": "0.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "673e061b2a134c0338b38813", "m_name": "<PERSON><PERSON><PERSON><PERSON>", "m_type": "<PERSON><PERSON><PERSON><PERSON>", "m_explainText": "<PERSON>re a Barbarian Hero.", "m_targetValue": "1.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "615c5305ff6375001e7eccb6", "m_name": "InitialWorkerCount", "m_type": "TotalWorkers", "m_explainText": "Have [TargetValue] workers employed\n<size=80%>[Value] more to hire.</size>", "m_targetValue": "3.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6866cf3962b06703167967b4", "m_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_type": "Kill<PERSON>haracter", "m_explainText": "Kill that greedy gate keeper bastard.", "m_targetValue": "0.00", "m_power": "<PERSON><PERSON><PERSON><PERSON>", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "63d40ae052bb410011b81585", "m_name": "None", "m_type": "None", "m_explainText": "", "m_targetValue": "0.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67a0c6d5fb671a1ac830a865", "m_name": "OpenArcadium", "m_type": "OpenArcadium", "m_explainText": "<PERSON><PERSON> Arcadi<PERSON> (bottom left)", "m_targetValue": "1.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67fb9249f4a7ca02dcee1660", "m_name": "PayMineWorker", "m_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_explainText": "Pay Doug £10,000.\n<size=60%>Or maybe not.</size>", "m_targetValue": "0.00", "m_power": "WaitForQuestActivate(FlowCharacter[DougMineWorker], 4)", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67c07d9316e7d702d5724762", "m_name": "PossessCharacter", "m_type": "PossessCharacter", "m_explainText": "Possess Any Character\n<size=80%>Return to Valmey.</size>", "m_targetValue": "1.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6761a8972722bc03082fff71", "m_name": "PossessDog", "m_type": "PossessAnimal", "m_explainText": "Possess <PERSON>", "m_targetValue": "1.00", "m_power": "QuestDog", "m_repeatCount": 0, "m_execute": "", "m_advisor": ""}, {"id": "674eeec22cde570302c66e37", "m_name": "PossessHero", "m_type": "PossessHero", "m_explainText": "Possess Your Hero", "m_targetValue": "1.00", "m_power": "Giant", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "66b7c24527da330031b292ce", "m_name": "Quest6Stones", "m_type": "QuestComplete", "m_explainText": "Set My Stones Singing", "m_targetValue": "1.00", "m_power": "MAQuest6Stones", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6811dbc5aa038202dd1cf1c2", "m_name": "Quest6StonesCircleComplete", "m_type": "QuestObjective", "m_explainText": "Place the stones in order,\nthen tap the tent", "m_targetValue": "1.00", "m_power": "MAQuest6Stones:CircleComplete", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "66cc4d9f0decf9027efffe1d", "m_name": "QuestAccept", "m_type": "QuestAccept", "m_explainText": "Accept or Reject The Quest", "m_targetValue": "1.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6808d07acbf27102e0695a36", "m_name": "QuestCaveBlockerMetalAccept", "m_type": "QuestAccept", "m_explainText": "Find the explorer at the cave entrance in Wyrmscar", "m_targetValue": "1.00", "m_power": "MAQuestCaveBlockerMetal", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6808c8037a122102c93a2fce", "m_name": "QuestCaveBlockerMetalComplete", "m_type": "QuestComplete", "m_explainText": "Have <PERSON> clear the cave in Wyrmscar", "m_targetValue": "1.00", "m_power": "MAQuestCaveBlockerMetal", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67e43e7191b0260305215734", "m_name": "QuestChickenFootballReturnChickens", "m_type": "QuestObjective", "m_explainText": "Return [TargetValue] chickens to their pen\n<size=80%>[Value] left.</size>", "m_targetValue": "10.00", "m_power": "MAQuestChickenFootball:Return<PERSON><PERSON><PERSON>s", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67f69dc06bd56c02c1708827", "m_name": "QuestCompleteSimonStones", "m_type": "QuestComplete", "m_explainText": "Complete the cleric's challenge", "m_targetValue": "1.00", "m_power": "MAQuestSimonStones", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6763060c8f7c4c0315c27cff", "m_name": "QuestDogCottersCollectBottle", "m_type": "QuestObjective", "m_explainText": "Find and take\\nthe Ointment", "m_targetValue": "1.00", "m_power": "MAQuestDogCotters:CollectBottle", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6761b4522722bc0308301760", "m_name": "QuestDogCottersReachCamp", "m_type": "PossessedInRange", "m_explainText": "Track down the\\nelusive salesman", "m_targetValue": "12.00", "m_power": "Pos[-127;146]", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6762ffc830892002e7264560", "m_name": "QuestDogCottersReturnBottle", "m_type": "PossessedInCharacterRange", "m_explainText": "Take the Ointment back\\nto Thomas <PERSON>tter", "m_targetValue": "10.00", "m_power": "<PERSON><PERSON><PERSON><PERSON>", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67d737f264e9870335739ab3", "m_name": "QuestDogForemanAccept", "m_type": "QuestAccept", "m_explainText": "Find the third cousin twice removed of King <PERSON><PERSON>", "m_targetValue": "1.00", "m_power": "MAQuestDogForeman", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67a4aa1908504f0323d1b87b", "m_name": "QuestDogForemanArriveTimelineComplete", "m_type": "QuestObjective", "m_explainText": "<PERSON>", "m_targetValue": "1.00", "m_power": "MAQuestDogForeman:ArriveTimelineComplete", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "679b974adb95b80306ebf0dd", "m_name": "QuestDogForemanCollectFragments", "m_type": "QuestObjective", "m_explainText": "Find the note fragments", "m_targetValue": "4.00", "m_power": "MAQuestDogForeman:CollectFragments", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "678e29cedca089031f0d0ad6", "m_name": "QuestDogForemanCollectHardhat", "m_type": "QuestObjective", "m_explainText": "Bring <PERSON> his forgotten lunch", "m_targetValue": "1.00", "m_power": "MAQuestDogForeman:CollectHardhat", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "678a74cc66f382031d063d89", "m_name": "QuestDogForemanCollectLantern", "m_type": "QuestObjective", "m_explainText": "Bring <PERSON> his forgotten lunch", "m_targetValue": "1.00", "m_power": "MAQuestDogForeman:CollectLantern", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "678e4827718e8602b4ba12ee", "m_name": "QuestDogForemanCollectNote", "m_type": "QuestObjective", "m_explainText": "Search the area for clues", "m_targetValue": "1.00", "m_power": "MAQuestDogForeman:CollectNote", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67f532a3aa4ca302d5745057", "m_name": "QuestDogForemanComplete", "m_type": "QuestComplete", "m_explainText": "Help the king's third cousin", "m_targetValue": "1.00", "m_power": "MAQuestDogForeman", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67a34ef309035402c43c3991", "m_name": "QuestDogForemanJump<PERSON>", "m_type": "QuestObjective", "m_explainText": "<PERSON>", "m_targetValue": "1.00", "m_power": "MAQuestDogForeman:<PERSON><PERSON><PERSON>", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67a249f0c2aa1602e4a225f4", "m_name": "QuestDogForemanReachCliff", "m_type": "PossessedInRange", "m_explainText": "<PERSON>", "m_targetValue": "5.00", "m_power": "Pos[-370;-74]", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67618bfd5e7e6d02b1abefac", "m_name": "QuestFindLostBoy", "m_type": "QuestAccept", "m_explainText": "Find the Lost Boy\n<size=80%>Look outside the gates by the Farm.</size>", "m_targetValue": "1.00", "m_power": "QuestLostBoy", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67f69af5f4a7ca02dce93556", "m_name": "QuestFindSimonStones", "m_type": "QuestAccept", "m_explainText": "Find the wise cleric by the singing stones of Briar Lake", "m_targetValue": "1.00", "m_power": "MAQuestSimonStones", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67cd805dd583c303224df39a", "m_name": "QuestFindStoneCircle", "m_type": "QuestIntro", "m_explainText": "Return to the cleric.", "m_targetValue": "1.00", "m_power": "MAQuest6Stones", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "679b70f66c864e136a513930", "m_name": "QuestJudgementComplete", "m_type": "QuestObjective", "m_explainText": "Decide their Fate", "m_targetValue": "1.00", "m_power": "MAQuestJudgement:InAny", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67a0ba704a83ef032a984bef", "m_name": "QuestJudgementGallowsTriggered", "m_type": "QuestObjective", "m_explainText": "Pull the Lever", "m_targetValue": "1.00", "m_power": "MAQuestJudgement:GallowsTriggered", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67b8b3d67808e20d9c03a5a2", "m_name": "QuestJudgementInTableSaw", "m_type": "QuestObjective", "m_explainText": "Decide their Fate", "m_targetValue": "1.00", "m_power": "MAQuestJudgement:InTableSaw", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "66d043d2ceaf020307cabc8c", "m_name": "QuestLostBoy", "m_type": "QuestComplete", "m_explainText": "Find the Lost Boy\n<size=80%>Return him to Oakridge Tavern.</size>", "m_targetValue": "1.00", "m_power": "QuestLostBoy", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67ea815af3289702ebfeb5b6", "m_name": "QuestLostBoyReturned", "m_type": "QuestObjective", "m_explainText": "Find the Lost Boy\n<size=80%>Return him to Oakridge Tavern.</size>", "m_targetValue": "1.00", "m_power": "QuestLostBoy:LostBoyReturned", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67d746c3ca7dba02fca59e5a", "m_name": "QuestMessageInBottle", "m_type": "QuestComplete", "m_explainText": "Find The Message In The Bottle.", "m_targetValue": "1.00", "m_power": "QuestMessageInBottle", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67d04a3469e54502e2385701", "m_name": "QuestMessageInBottleCollectBottle", "m_type": "QuestObjective", "m_explainText": "Scan the coastline where the pirates set sail. Find <PERSON>'s message.", "m_targetValue": "1.00", "m_power": "MAQuestMessageInBottle:CollectMessageBottle", "m_repeatCount": 0, "m_execute": "", "m_advisor": ""}, {"id": "67d2af843df69302ee68286d", "m_name": "QuestMessageInBottleDeliverMessage", "m_type": "QuestObjective", "m_explainText": "Take the message back to poor <PERSON>", "m_targetValue": "1.00", "m_power": "MAQuestMessageInBottle:DeliverMessage", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67e17c7939bc1702f15fc828", "m_name": "QuestPirateDeliverCloth", "m_type": "DeliverResources", "m_explainText": "Deliver [TargetValue] Cloth for the pirate ship sails\n<size=80%>[Value] more needed.</size>", "m_targetValue": "6.00", "m_power": "QuestPirateStockpile:Fabric", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67e17bf0238ce303016e599d", "m_name": "QuestPirateDeliverMetal", "m_type": "DeliverResources", "m_explainText": "Deliver [TargetValue] Metal to make nails for the pirate ship\n<size=80%>[Value] more needed.</size>", "m_targetValue": "6.00", "m_power": "QuestPirateStockpile:Metal", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67dd83ef59a34102d274ff08", "m_name": "QuestPirateDeliverWood", "m_type": "DeliverResources", "m_explainText": "Deliver [TargetValue] Wood to build the pirate ship\n<size=80%>[Value] more needed.</size>", "m_targetValue": "6.00", "m_power": "QuestPirateStockpile:<PERSON><PERSON>", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67e6935f0d5a5002d0909408", "m_name": "QuestReachLostBoy", "m_type": "PossessedInCharacterRange", "m_explainText": "Find the Lost Boy\n<size=80%>Look outside the gates by the Farm.</size>", "m_targetValue": "15.00", "m_power": "LostBoy\n", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "689eddc769e8bc02ddc3c003", "m_name": "QuestRumBarrelsAccept", "m_type": "QuestAccept", "m_explainText": "Help the Pirates with their rum problem", "m_targetValue": "1.00", "m_power": "MAQuestRumBarrels", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "680936e664750e02f4ab13a5", "m_name": "QuestRumBarrelsComplete", "m_type": "QuestComplete", "m_explainText": "Help the Pirates with their rum problem", "m_targetValue": "1.00", "m_power": "MAQuestRumBarrels", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "680917d7eca77c02fd205257", "m_name": "QuestRumBarrelsReadNotice", "m_type": "QuestObjective", "m_explainText": "Read the notice at the cave entrance", "m_targetValue": "1.00", "m_power": "MAQuestRumBarrels:ReadNotice", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67f915e56666545787867b69", "m_name": "QuestRumBarrelsSmashAll", "m_type": "QuestObjective", "m_explainText": "Smash all barrels to check if they're empty\n<size=80%>[Value] Barrels remain</size>", "m_targetValue": "15.00", "m_power": "MAQuestRumBarrels:SmashAll", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6778057471c69e0303156fcf", "m_name": "QuestSimonStonesAttemptMade", "m_type": "QuestObjective", "m_explainText": "Repeat the melodies", "m_targetValue": "1.00", "m_power": "MAQuestSimonStones:AttemptMade", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "677936bb947bcd02ba58e2db", "m_name": "QuestSimonStonesIsFirstAttempt", "m_type": "QuestObjective", "m_explainText": "Repeat the melodies", "m_targetValue": "1.00", "m_power": "MAQuestSimonStones:IsFirstAttempt", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "677827ac122f1f02d2c1638c", "m_name": "QuestSimonStonesPuzzleCompleted", "m_type": "QuestObjective", "m_explainText": "Repeat the melodies", "m_targetValue": "1.00", "m_power": "MAQuestSimonStones:PuzzleCompleted", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6778270f8b138702b1e14bc9", "m_name": "QuestSimonStonesTuneCompleted", "m_type": "QuestObjective", "m_explainText": "Repeat the melodies", "m_targetValue": "1.00", "m_power": "MAQuestSimonStones:TuneCompleted", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "688b7e6130bda802bf5bb232", "m_name": "QuestStigmataBallHeld", "m_type": "QuestObjective", "m_explainText": "Locate the artefact near the Oakshade Crossing bridge", "m_targetValue": "1.00", "m_power": "MAQuestStigmata:BallHeld", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "688b3cbd74256c02cd2f8330", "m_name": "QuestStigmataComplete", "m_type": "QuestComplete", "m_explainText": "Free your hand from the trap", "m_targetValue": "1.00", "m_power": "MAQuestStigmata", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "688b85818d496202d9eda0df", "m_name": "QuestStigmataTrapTriggered", "m_type": "QuestObjective", "m_explainText": "Locate the artefact near the Oakshade Crossing bridge and bring it to Cricket at Oakridge Crypt", "m_targetValue": "1.00", "m_power": "MAQuestStigmata:TrapTriggered", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67ed645db709b102c5195944", "m_name": "QuestValmeysInterludeArriveAtRidgeView", "m_type": "PossessedInRange", "m_explainText": "Lead <PERSON><PERSON><PERSON> up the path to Ridge View", "m_targetValue": "15.00", "m_power": "Pos[-327.07;-68.33]", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67c1eddc0c2cef0311d80d03", "m_name": "QuestValmeysInterludeArriveAtStoneCircle", "m_type": "PossessedInRange", "m_explainText": "Lead <PERSON><PERSON><PERSON> to the Stone Circle", "m_targetValue": "12.00", "m_power": "Pos[-212.52;5.9]", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67c07e7e3bbf15030e5832c4", "m_name": "QuestValmeysInterludeReturnToValmey", "m_type": "PossessedInCharacterRange", "m_explainText": "Return to Valmey", "m_targetValue": "8.00", "m_power": "<PERSON><PERSON><PERSON>", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6814dda6ea7b9602c7d0dff0", "m_name": "QuestWaspProblemSmashAll", "m_type": "QuestObjective", "m_explainText": "Destroy all the wasp nests\n<size=80%>[Value] left to smash!</size>", "m_targetValue": "5.00", "m_power": "MAQuestWaspProblem:SmashAll", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "68012b90062c0102e0885999", "m_name": "QuestWyrmscarBridgeComplete", "m_type": "QuestObjective", "m_explainText": "Rebuild the Bridge", "m_targetValue": "1.00", "m_power": "MAQuestWyrmscarBridge:BridgeComplete", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6843e3ea4310790302d20a39", "m_name": "QuestWyrmscarBridgeTimberRequired", "m_type": "QuestObjective", "m_explainText": "Rebuild Wyrmscar Bridge\n<size=80%>Deliver [Value] Timber</size>", "m_targetValue": "0.00", "m_power": "MAQuestWyrmscarBridge:TimberRequired", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67ebf024dff8e302c7bef72e", "m_name": "RebuildCottonFarm", "m_type": "RebuildBuilding", "m_explainText": "Rebuild the Cotton Farm", "m_targetValue": "1.00", "m_power": "Building[CottonFarm]\nStockIn", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67113ae6b63f5202d55ee2d3", "m_name": "RebuildFactory", "m_type": "RebuildBuilding", "m_explainText": "Rebuild the Factory", "m_targetValue": "1.00", "m_power": "Building[Factory]\nMA_Factory_Action\nAesthetic\nRoof\nStockIn\nEntrance\nStockOut\nRoof\nChimney\nGUIOrderInfo\n", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "670fd560f20a6e02bcf95377", "m_name": "RebuildFarm", "m_type": "RebuildBuilding", "m_explainText": "Rebuild the Farm", "m_targetValue": "1.00", "m_power": "Building[101]\nEntrance\nAesthetic\nAesthetic\nAesthetic\nStockOut\nMA_Farm_Action\nAesthetic\nRoof\nRoof\n\n\n", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67112d1b9fca7102e2c55be6", "m_name": "RebuildMill", "m_type": "RebuildBuilding", "m_explainText": "Rebuild the Mill", "m_targetValue": "1.00", "m_power": "Building[224]\nMA_Produce_Mill_Action\nStockOut\nEntrance\nStockIn\nAesthetic\nAesthetic\n", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67d974e9cfa09102c09624ac", "m_name": "RoadBuildMode", "m_type": "RoadBuildMode", "m_explainText": "Toggle Wall Building\n<size=80%>Hint: Bottom left icon.</size>", "m_targetValue": "1.00", "m_power": "RoadBuild", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67ed379c7e32ee02e2008c1a", "m_name": "SellClothProducts", "m_type": "SellProducts", "m_explainText": "Sell [TargetValue] Cloth Products\n<size=80%>[Value] more to go.</size>", "m_targetValue": "2.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6137733582342b001eda8b75", "m_name": "SellFoodProducts", "m_type": "SellProducts", "m_explainText": "Sell [TargetValue] Food Products\n<size=80%>[Value] more to go.</size>", "m_targetValue": "2.00", "m_power": "Peoples", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67d73e8ee0852902d67716bd", "m_name": "SellRoyalProducts", "m_type": "SellProducts", "m_explainText": "Sell [TargetValue] Royal Products\n<size=80%>[Value] more to go.</size>", "m_targetValue": "2.00", "m_power": "Royal", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "684bf34ec7fa2d02e385c32e", "m_name": "SetHeroPatrol", "m_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_explainText": "Set Hero's Patrol Radius\n<size=80%>Hint: Pickup Hero & Press P.</size>", "m_targetValue": "0.00", "m_power": "WaitForCharacterPatrol(Character[Giant])", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67dd2a3a99ffd7030a013abb", "m_name": "SingingStonesDiscovered", "m_type": "CamOrPossessedInRange", "m_explainText": "Seek out the cleric meditating in a sacred space. \\n Hint: Look along the roadsides…", "m_targetValue": "10.00", "m_power": "Pos[-218.54;-1.87]", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67976eef1dedb302b12a2f8e", "m_name": "SpeedupToNight", "m_type": "SpeedupToNight", "m_explainText": "Use the Speedup to night button on the calendar.", "m_targetValue": "1.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6581a5ad5305620029bc05c0", "m_name": "TapHereToBegin", "m_type": "TapHereToBegin", "m_explainText": "Tap here to begin Chapter 2", "m_targetValue": "0.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "673e1dced0e9a102f4c02d71", "m_name": "TrackHeroKills", "m_type": "TrackHeroKills", "m_explainText": "Kill [TargetValue] monsters with the Hero\n<size=80%>[Value] more to go!\n\nHint: Kills made by you directly do not count.</size>\n", "m_targetValue": "4.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "688a441c61db9702c4a72e82", "m_name": "UnlockArmouryPack", "m_type": "UnlockResearchItem", "m_explainText": "Unlock [Title] in Arcadium\n<size=80%>You need [Money][Favours]</size>", "m_targetValue": "1.00", "m_power": "ArmourerPack", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67d0a08c3342780319683f16", "m_name": "UnlockBeacons", "m_type": "UnlockResearch", "m_explainText": "Unlock [Title] in Arcadium\n<size=80%>You need [Money][Favours].</size>", "m_targetValue": "1.00", "m_power": "UnlockBeacons", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67c04611f9b8f802ba28e9e9", "m_name": "UnlockBuildLumberMill", "m_type": "UnlockResearch", "m_explainText": "Unlock [Title] in Arcadium\n<size=80%>You need [Money][Favours].</size>", "m_targetValue": "1.00", "m_power": "BuildLumberMill", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67ba27bc0b1b3e0bee0fc8af", "m_name": "UnlockBuildTurret", "m_type": "UnlockResearch", "m_explainText": "Unlock [Title] in the Arcadium\n<size=80%>You need [Money][Favours].</size>", "m_targetValue": "1.00", "m_power": "BuildTurret", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67cee3cdaf050e02dcb73efb", "m_name": "UnlockBuildWeaponsmith", "m_type": "UnlockResearch", "m_explainText": "Unlock [Title] in Arcadium\n<size=80%>You need [Money][Favours]</size>", "m_targetValue": "0.00", "m_power": "BuildWeaponsmith", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "684c0beb3e95720304bd71d5", "m_name": "UnlockHandPowersLightningL2", "m_type": "UnlockResearch", "m_explainText": "Unlock [Title] in Arcadium\n<size=80%>You need [Money][Favours]</size>", "m_targetValue": "1.00", "m_power": "m_handPowerLightningLevel=2", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67c0b15c39e1c702e4282329", "m_name": "UnlockHarvestMysticRunes", "m_type": "UnlockResearch", "m_explainText": "Unlock [Title] in the Arcadium\n<size=80%>You need [Money][Favours].</size>", "m_targetValue": "1.00", "m_power": "m_harvestMysticRunes", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "672a3dd1f3e99e0312e2086b", "m_name": "UnlockHeroesGuild", "m_type": "UnlockResearch", "m_explainText": "Unlock [Title] in Arcadium\n<size=80%>You need [Money][Favours]</size>", "m_targetValue": "1.00", "m_power": "BuildHeroGuild", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6793f71d4a2cf102d84d6573", "m_name": "UnlockLightning", "m_type": "UnlockResearch", "m_explainText": "Unlock [Title] in Arcadium\n<size=80%>You need [Money][Favours].</size>", "m_targetValue": "1.00", "m_power": "UnlockLightning", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67ba3e9220fff702c3668182", "m_name": "UnlockLumberMill", "m_type": "UnlockResearch", "m_explainText": "Unlock [Title] in Arcadium\n<size=80%>You need [Money][Favours].</size>", "m_targetValue": "0.00", "m_power": "BuildLumberMill", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "657837337c1cad00284bfbc7", "m_name": "UnlockMetalRegion", "m_type": "UnlockRegion", "m_explainText": "Unlock the Metal Region", "m_targetValue": "1.00", "m_power": "Metal", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67601ba4b71d30031c126f01", "m_name": "UnlockWallBuilding", "m_type": "UnlockResearch", "m_explainText": "Purchase [Title] in Arcadium\n<size=80%>You need [Money][Favours].</size>", "m_targetValue": "0.00", "m_power": "m_designWalls", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "68669a61d35a0b02f52ead7a", "m_name": "ValmeyBriarLakeTavernDiscovered", "m_type": "CamOrPossessedInRange", "m_explainText": "Go and see <PERSON><PERSON><PERSON> at the Tavern in Briar Lake.", "m_targetValue": "10.00", "m_power": "Pos[-312.23;528.59]", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67f2ce03b2b05a03045bb7ed", "m_name": "ValmeyDiscoverdOutsideBriarLakesTavern", "m_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_explainText": "Seek out <PERSON><PERSON><PERSON> at Briar Lakes Tavern", "m_targetValue": "0.00", "m_power": "WaitForQuestActivate(FlowCharacter[Valmey], 4)", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67efc0f0a7eabd02fc40f58a", "m_name": "ValmeyDiscoverdOutsideTavern", "m_type": "CamOrPossessedInRange", "m_explainText": "Seek out <PERSON><PERSON><PERSON> outside Briar Lake Tavern.", "m_targetValue": "10.00", "m_power": "Building[BriarLakeTavern]", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "681b7fc1887bb0030c820b16", "m_name": "WaitForFactoryOrder", "m_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_explainText": "Assign Order\n<size=80%>Drag order to Factory</size>", "m_targetValue": "0.00", "m_power": "WaitForOrderTo(Building[Factory])", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6853ea6d64965b02dbb9e827", "m_name": "WalkToOakridgeBeacon", "m_type": "PossessedInRange", "m_explainText": "Possess the Hero and walk to the beacon.", "m_targetValue": "20.00", "m_power": "Pos[-213.9;60.6]", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "67c8300232e87002bd57ac20", "m_name": "WorkerCount13", "m_type": "TotalWorkers", "m_explainText": "Have [TargetValue] workers employed\n<size=80%>[Value] more to hire</size>\n<size=80%>[Param1] more bedrooms required</size>\n<size=80%>[Param2] more workstations required</size>", "m_targetValue": "13.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}, {"id": "6137737a12949c001ede2d07", "m_name": "WorkerMines", "m_type": "WorkerMines", "m_explainText": "Workers mine [Value] resources", "m_targetValue": "10.00", "m_power": "", "m_repeatCount": "", "m_execute": "", "m_advisor": ""}]