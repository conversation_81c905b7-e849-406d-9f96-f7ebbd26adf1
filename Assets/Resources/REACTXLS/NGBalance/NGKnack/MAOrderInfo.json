[{"id": "66c337c4117dcd028078ecba", "m_indexer": "armourorder:0010", "m_name": "Male Armour", "m_useName": "True", "m_index": "0010", "m_mAOrderGiver": "Player", "m_type": "Repeat", "m_product": "Armour", "m_blockName": "armourorder", "m_collectType": "MaleHero", "m_tags": "", "m_tagHint": "The best form of defence is good armour.", "m_cardDescription": "Male Armour", "m_lowQuantity": 1, "m_highQuantity": 1, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "", "m_design": "1|<PERSON>_<PERSON><PERSON>_Mannequin@|0|"}, {"id": "6814ce1ae6f1aa02f5796a7e", "m_indexer": "armourorder:0011", "m_name": "Female Armour", "m_useName": "True", "m_index": "0011", "m_mAOrderGiver": "Player", "m_type": "Repeat", "m_product": "Armour Female", "m_blockName": "armourorder", "m_collectType": "FemaleHero", "m_tags": "", "m_tagHint": "The best for of defence is good armour.", "m_cardDescription": "Female Armour", "m_lowQuantity": 1, "m_highQuantity": 1, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "", "m_design": "1|<PERSON>_<PERSON><PERSON>_Mannequin_Female@|0|"}, {"id": "67ea611b0b3c1b02eaba43d1", "m_indexer": "ArmourProduct:0010", "m_name": "Simple Male Armour", "m_useName": "", "m_index": "0010", "m_mAOrderGiver": "the_honourable_hand", "m_type": "Repeat", "m_product": "Armour", "m_blockName": "ArmourProduct", "m_collectType": "MaleHero", "m_tags": "", "m_tagHint": "", "m_cardDescription": "Simple Armour", "m_lowQuantity": 4, "m_highQuantity": 4, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "PeopleFavour2", "m_design": "1|<PERSON>_<PERSON><PERSON>_Mannequin@|0|"}, {"id": "67ea62690b3c1b02eaba4a0c", "m_indexer": "ArmourProduct:0020", "m_name": "Flamboyant Male Armour", "m_useName": "", "m_index": "0020", "m_mAOrderGiver": "the_honourable_hand", "m_type": "Repeat", "m_product": "Armour", "m_blockName": "ArmourProduct", "m_collectType": "MaleHero", "m_tags": "", "m_tagHint": "", "m_cardDescription": "Flamboyant Armour", "m_lowQuantity": 12, "m_highQuantity": 12, "m_lowDesignScore": "0.002", "m_highDesignScore": "0.003", "m_rewards": "RoyalFavour4", "m_design": "1|<PERSON>_<PERSON><PERSON>_Mannequin@|0|"}, {"id": "67ea63be74b25502e5ba5b6f", "m_indexer": "ArmourProduct:0030", "m_name": "<PERSON><PERSON>", "m_useName": "", "m_index": "0030", "m_mAOrderGiver": "the_honourable_hand", "m_type": "Repeat", "m_product": "Armour Female", "m_blockName": "ArmourProduct", "m_collectType": "FemaleHero", "m_tags": "", "m_tagHint": "", "m_cardDescription": "<PERSON><PERSON>", "m_lowQuantity": 20, "m_highQuantity": 20, "m_lowDesignScore": "0.003", "m_highDesignScore": "0.004", "m_rewards": "RoyalFavour5", "m_design": "1|<PERSON>_<PERSON><PERSON>_Mannequin_Female@|0|"}, {"id": "67f68f2082179a03088555a9", "m_indexer": "BriarLakeQuestTable:0010", "m_name": "Bandit King's New clothes", "m_useName": "", "m_index": "0010", "m_mAOrderGiver": "bandit_king", "m_type": "OneShot", "m_product": "Clothing", "m_blockName": "BriarLakeQuestTable", "m_collectType": "", "m_tags": "Shoes|Hat|Trousers|Coat|Stylish", "m_tagHint": "Stylish is rare", "m_cardDescription": "<PERSON><PERSON><PERSON> will only wear the finest of clothes fit for a king. Impress him with his dress.", "m_lowQuantity": 1, "m_highQuantity": 1, "m_lowDesignScore": "0.400", "m_highDesignScore": "0.400", "m_rewards": "", "m_design": "1|<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_Mannequin@|0|"}, {"id": "68593b4dab1c0b1757bda255", "m_indexer": "BriarLakeQuestTable:0020", "m_name": "A Dress For Valmey", "m_useName": "", "m_index": "0020", "m_mAOrderGiver": "valmey", "m_type": "OneShot", "m_product": "Clothing Female", "m_blockName": "BriarLakeQuestTable", "m_collectType": "", "m_tags": "", "m_tagHint": "A step up from dowdy will make her feel proudy", "m_cardDescription": "Give <PERSON><PERSON><PERSON> a lift, design her a wearable gift.", "m_lowQuantity": 1, "m_highQuantity": 1, "m_lowDesignScore": "0.200", "m_highDesignScore": "0.200", "m_rewards": "PeopleFavour5", "m_design": "1|<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_Mannequin@|0|"}, {"id": "680f61500ace1502d63f69e3", "m_indexer": "BriarWoodQuestTable:0010", "m_name": "Bandit King's New clothes", "m_useName": "", "m_index": "0010", "m_mAOrderGiver": "bandit_king", "m_type": "OneShot", "m_product": "Clothing", "m_blockName": "BriarWoodQuestTable", "m_collectType": "", "m_tags": "", "m_tagHint": "", "m_cardDescription": "<PERSON><PERSON><PERSON> will only wear the finest of clothes fit for a king. Impress him with his dress.", "m_lowQuantity": 1, "m_highQuantity": 1, "m_lowDesignScore": "0.400", "m_highDesignScore": "0.400", "m_rewards": "", "m_design": "1|<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_Mannequin@|0|"}, {"id": "65fd6ab0bb4f100027e63d5e", "m_indexer": "Chapter 1_Diary:0020", "m_name": "tbd.", "m_useName": "", "m_index": "0020", "m_mAOrderGiver": "silvia_plinth", "m_type": "Repeat", "m_product": "Food", "m_blockName": "Chapter 1_Diary", "m_collectType": "", "m_tags": "Bread|Dairy|Sandwich|Salad|Healthy", "m_tagHint": "Fresh bread's a good place to start.", "m_cardDescription": "Being poor doesn't mean you have to eat poor. <PERSON><PERSON><PERSON> wants to teach her nearest and dearest a thing or two about nutrition. She wants some healthy option lunchtime meals.", "m_lowQuantity": 4, "m_highQuantity": 8, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "PeopleFavour2", "m_design": ""}, {"id": "65fd6aa3ee123200274aa8e2", "m_indexer": "Chapter 1_Diary:0030", "m_name": "tbd.", "m_useName": "", "m_index": "0030", "m_mAOrderGiver": "silvia_plinth", "m_type": "Repeat", "m_product": "Food", "m_blockName": "Chapter 1_Diary", "m_collectType": "", "m_tags": "Expensive|Protein|Meat|Dairy", "m_tagHint": "Protein at this stage in life is perfect.", "m_cardDescription": "There's healthy diet's and there's growing bodies. <PERSON><PERSON><PERSON> needs to feed the teenagers in her brood. Make sure she gets something that will fuel their maturing bodies.", "m_lowQuantity": 8, "m_highQuantity": 16, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "PeopleFavour3", "m_design": ""}, {"id": "65f99918546b7800278e44e7", "m_indexer": "Chapter 1_old:0050", "m_name": "Egg n'Bread Surprise", "m_useName": "", "m_index": "0050", "m_mAOrderGiver": "silvia_plinth", "m_type": "Repeat", "m_product": "Food", "m_blockName": "Chapter 1_old", "m_collectType": "", "m_tags": "Cheap", "m_tagHint": "Review the Design Table information panels.", "m_cardDescription": "Disaster strikes! Lord <PERSON><PERSON> of the Gatsby Chocolate empire, has fired every member of <PERSON><PERSON><PERSON>'s union. She needs the cheap food with high nutritional value to feed them!", "m_lowQuantity": 20, "m_highQuantity": 20, "m_lowDesignScore": "0.220", "m_highDesignScore": "0.220", "m_rewards": "PeopleFavour4", "m_design": ""}, {"id": "65e07ea4042eab0029aadaed", "m_indexer": "Chapter 1_old:0060", "m_name": "Gri-mace-ing <PERSON>", "m_useName": "", "m_index": "0060", "m_mAOrderGiver": "silvia_plinth", "m_type": "Repeat", "m_product": "Food", "m_blockName": "Chapter 1_old", "m_collectType": "", "m_tags": "Unhealthy|Tasteless|Sandwich", "m_tagHint": "<PERSON><PERSON> makes mincemeat of a healthy gut.", "m_cardDescription": "A member of the aristocracy asks for a secret meeting. Something about funding <PERSON><PERSON><PERSON>'s activities. But <PERSON><PERSON><PERSON> trusts no blue blood. She's looking for some nasty food.", "m_lowQuantity": 20, "m_highQuantity": 30, "m_lowDesignScore": "0.210", "m_highDesignScore": "0.210", "m_rewards": "LordsFavour3", "m_design": ""}, {"id": "65e0813e6686b30027257d78", "m_indexer": "Chapter 1_old:0090", "m_name": "Regal Rumble Order", "m_useName": "", "m_index": "0090", "m_mAOrderGiver": "the_honourable_hand", "m_type": "Repeat", "m_product": "Weapons", "m_blockName": "Chapter 1_old", "m_collectType": "", "m_tags": "Swords", "m_tagHint": "Good swords make a lasting impression.", "m_cardDescription": "When the Honourable Hand speaks, it is the words of the King he utters. His majesty demands the support of Albion's factories for Albion's troops on the battlefields. The Hand calls for Weapons. ", "m_lowQuantity": 55, "m_highQuantity": 100, "m_lowDesignScore": "0.410", "m_highDesignScore": "0.410", "m_rewards": "RoyalFavour2", "m_design": ""}, {"id": "65fd6a89a4129500280b2129", "m_indexer": "Chapter 1_old:0100", "m_name": "Tuck Shoppe Treats", "m_useName": "", "m_index": "0100", "m_mAOrderGiver": "macio_gourd", "m_type": "Repeat", "m_product": "Food", "m_blockName": "Chapter 1_old", "m_collectType": "", "m_tags": "Pie|Fish|Animal_Product|Baked|Protein|Dairy", "m_tagHint": "Albion is famous for its pies.", "m_cardDescription": "Canny Continental, <PERSON><PERSON>, knows an opportunity when he sees one. War means money! <PERSON><PERSON><PERSON> wants good quality homemade dishes to sell to the invading generals.", "m_lowQuantity": 12, "m_highQuantity": 43, "m_lowDesignScore": "0.210", "m_highDesignScore": "0.210", "m_rewards": "LordsFavour2", "m_design": ""}, {"id": "65df4dea85cf300037318ce4", "m_indexer": "Chapter 2:0010", "m_name": "Basic Food Money Order", "m_useName": "", "m_index": "0010", "m_mAOrderGiver": "silvia_plinth", "m_type": "Repeat", "m_product": "Food", "m_blockName": "Chapter 2", "m_collectType": "", "m_tags": "", "m_tagHint": "", "m_cardDescription": "Even the most ardent trades unionist has to take a break now and then. It's <PERSON><PERSON><PERSON>'s birthday soon, she's having a party (self-organised, natch) and needs party foods.\n<Hint:> <PERSON><PERSON> is dreamy.", "m_lowQuantity": 10, "m_highQuantity": 15, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "PeopleFavour3", "m_design": ""}, {"id": "65e081146686b30027257cae", "m_indexer": "Chapter 2:0020", "m_name": "Blustering Lords Order B", "m_useName": "", "m_index": "0020", "m_mAOrderGiver": "macio_gourd", "m_type": "Repeat", "m_product": "Weapons", "m_blockName": "Chapter 2", "m_collectType": "", "m_tags": "", "m_tagHint": "", "m_cardDescription": "Never mix business with pleasure is the motto of mid-league merchants. In <PERSON><PERSON>'s eyes, the two are one. <PERSON><PERSON><PERSON> is hosting a fabulous party for all of his business associates. It will be the talk of the town for years to come. The food has to reflect that. Give <PERSON><PERSON><PERSON> the meal of a lifetime.\n<Hint:> Half the eating is in the seeing. Stack it high, knock 'em dead.", "m_lowQuantity": 10, "m_highQuantity": 50, "m_lowDesignScore": "50.000", "m_highDesignScore": "100.000", "m_rewards": "RoyalFavour3", "m_design": ""}, {"id": "65e07dcb660cfa0026616a55", "m_indexer": "Chapter 2:0030", "m_name": "Basic Food Money Order C", "m_useName": "", "m_index": "0030", "m_mAOrderGiver": "silvia_plinth", "m_type": "Repeat", "m_product": "Food", "m_blockName": "Chapter 2", "m_collectType": "", "m_tags": "", "m_tagHint": "", "m_cardDescription": "Two twins, 19 years old, working in the same factory, lost the same arm in the same machine not two days apart. Out on their ear with no severance pay, their chances of survival are slim to nothing. <PERSON><PERSON><PERSON> must see they're taken care of.\nServe her fresh, healthy, heavy dishes that these youngsters can keep in their pantry for the hard times ahead.\n<Hint:> Avoid soft vegetables and meat if you want a long shelf life.", "m_lowQuantity": 18, "m_highQuantity": 30, "m_lowDesignScore": "12.000", "m_highDesignScore": "40.000", "m_rewards": "PeopleFavour3", "m_design": ""}, {"id": "65e0811add41f400291640e5", "m_indexer": "Chapter 2:0040", "m_name": "Blustering Lords Order C", "m_useName": "", "m_index": "0040", "m_mAOrderGiver": "macio_gourd", "m_type": "Repeat", "m_product": "Food", "m_blockName": "Chapter 2", "m_collectType": "", "m_tags": "", "m_tagHint": "", "m_cardDescription": "It's the Festival of Egg back in <PERSON><PERSON>'s home town. A great day if you like throwing eggs into the faces of your neighbours. An even better day if you're an importer of seed baring foodstuffs.\n<PERSON><PERSON> is on the lookout for anything that fits the bill.\n<Hint:> Fried eggs fly like a discus...", "m_lowQuantity": 60, "m_highQuantity": 120, "m_lowDesignScore": "30.000", "m_highDesignScore": "68.000", "m_rewards": "RoyalFavour3", "m_design": ""}, {"id": "65e0814af9447f002711212a", "m_indexer": "Chapter 2:0050", "m_name": "Regal Royals Order B", "m_useName": "", "m_index": "0050", "m_mAOrderGiver": "the_honourable_hand", "m_type": "Repeat", "m_product": "Food", "m_blockName": "Chapter 2", "m_collectType": "", "m_tags": "", "m_tagHint": "", "m_cardDescription": "His Majesty will be conducting field trials in the region. The Hand requires local producers to supply the royal household with suitable meals for the cavalry. \n<Hint:> Light meals with wings are best for the fleet footed.", "m_lowQuantity": 65, "m_highQuantity": 90, "m_lowDesignScore": "70.000", "m_highDesignScore": "140.000", "m_rewards": "RoyalFavour3", "m_design": ""}, {"id": "65e08153660cfa0026618938", "m_indexer": "Chapter 2:0060", "m_name": "Regal Royals Order C", "m_useName": "", "m_index": "0060", "m_mAOrderGiver": "the_honourable_hand", "m_type": "Repeat", "m_product": "Weapons", "m_blockName": "Chapter 2", "m_collectType": "", "m_tags": "", "m_tagHint": "", "m_cardDescription": "Reign in blood! So shout the troops as they march rank and file past the King's banner. The Honourable Hand wishes to reward the 7th Battalion with upgraded weaponry. He asks for high quality arms.\n<Hint:> Axes spread the contents of an enemy's veins around for miles if you swing them right.", "m_lowQuantity": 75, "m_highQuantity": 80, "m_lowDesignScore": "80.000", "m_highDesignScore": "160.000", "m_rewards": "RoyalFavour4", "m_design": ""}, {"id": "65df4dea85cf300037318d08", "m_indexer": "Chapter 3:0010", "m_name": "Basic Food Money Order A", "m_useName": "", "m_index": "0010", "m_mAOrderGiver": "silvia_plinth", "m_type": "Repeat", "m_product": "Food", "m_blockName": "Chapter 3", "m_collectType": "", "m_tags": "", "m_tagHint": "", "m_cardDescription": "An outbreak of scurvy among the workers of Lingorn in the North of Albion has shut the town's factory, the owners refusing to feed or pay their workers while they do no work.\nShe wants food, in large quantities, to keep them all going.\n<Hint:> A broth is just the ticket to keep the spirits up.", "m_lowQuantity": 10, "m_highQuantity": 15, "m_lowDesignScore": "10.000", "m_highDesignScore": "30.000", "m_rewards": "Give Research Lab", "m_design": ""}, {"id": "65df4dea85cf300037318cf0", "m_indexer": "Chapter 3:0020", "m_name": "Basic Royal Order", "m_useName": "", "m_index": "0020", "m_mAOrderGiver": "silvia_plinth", "m_type": "Repeat", "m_product": "Food", "m_blockName": "Chapter 3", "m_collectType": "", "m_tags": "", "m_tagHint": "", "m_cardDescription": "<PERSON><PERSON>h's got herself a side hustle, she's inherited her sister's food cart and is sending her youngest off to the joust to sell food to the poshos.\nShe wants deep filled pies.\n<Hint:> Just because they're posh, doesn't mean they know what's swimming in the gravy.", "m_lowQuantity": 50, "m_highQuantity": 100, "m_lowDesignScore": "40.000", "m_highDesignScore": "60.000", "m_rewards": "RoyalFavour4", "m_design": ""}, {"id": "65df4dea85cf300037318d1f", "m_indexer": "Chapter 3:0030", "m_name": "Basic Wood Favours", "m_useName": "", "m_index": "0030", "m_mAOrderGiver": "macio_gourd", "m_type": "Repeat", "m_product": "Food", "m_blockName": "Chapter 3", "m_collectType": "", "m_tags": "", "m_tagHint": "", "m_cardDescription": "When one is a member of the ruling classes on the continent, one must always appear superior. <PERSON><PERSON><PERSON><PERSON>, a man with the greatest of tastes, demands that he and all of his entourage eat well.\nSupply him with the finest dishes for a bijou supper with the staff.\n<Hint:> Cold meats and hot gravy is all the rage in the capital.", "m_lowQuantity": 12, "m_highQuantity": 18, "m_lowDesignScore": "12.000", "m_highDesignScore": "34.000", "m_rewards": "PeopleFavour3", "m_design": ""}, {"id": "65df4dea85cf300037318cfc", "m_indexer": "Chapter 3:0040", "m_name": "Easy Royal Order A", "m_useName": "", "m_index": "0040", "m_mAOrderGiver": "the_honourable_hand", "m_type": "Repeat", "m_product": "Food", "m_blockName": "Chapter 3", "m_collectType": "", "m_tags": "", "m_tagHint": "", "m_cardDescription": "A great victory has been had. His Majesty is in fine spirits. the Honourable Hand has been tasked with delivering a feast to the front lies.\nHe requires enough hot food to feed an army.\n<Hint:> Cheese comes with cheers.", "m_lowQuantity": 200, "m_highQuantity": 300, "m_lowDesignScore": "20.000", "m_highDesignScore": "20.000", "m_rewards": "RoyalFavour5", "m_design": ""}, {"id": "65df4dea85cf300037318d11", "m_indexer": "Chapter 5:0010", "m_name": "Basic Food Money Order", "m_useName": "", "m_index": "0010", "m_mAOrderGiver": "macio_gourd", "m_type": "Repeat", "m_product": "Food", "m_blockName": "Chapter 5", "m_collectType": "", "m_tags": "", "m_tagHint": "", "m_cardDescription": "<PERSON><PERSON>'s never been one to shy away from competition. The Count <PERSON> has bet the cavaliere that he can throw a better party than <PERSON><PERSON>. To refuse is to lose face, to lose is to lose your standing in the guild halls from here to Alahando. <PERSON><PERSON>, however, does not intend to do either.\nHe needs a table laden with food.\n<Hint:> Resource heavy items are what counts. Those who know, know.", "m_lowQuantity": 23, "m_highQuantity": 50, "m_lowDesignScore": "23.000", "m_highDesignScore": "26.000", "m_rewards": "RoyalFavour3", "m_design": ""}, {"id": "65df4dea85cf300037318d18", "m_indexer": "Chapter 5:0020", "m_name": "Basic Metal Money", "m_useName": "", "m_index": "0020", "m_mAOrderGiver": "the_honourable_hand", "m_type": "Repeat", "m_product": "Food", "m_blockName": "Chapter 5", "m_collectType": "", "m_tags": "", "m_tagHint": "", "m_cardDescription": "King <PERSON><PERSON> will soon welcome the Queen of the Kalmar Union, a powerful ally. this meeting must be fruitful. The Hand seeks the finest foods for the finest of visitors.\n<Hint:> Tomatoes do not exist in Kalmar.", "m_lowQuantity": 34, "m_highQuantity": 36, "m_lowDesignScore": "50.000", "m_highDesignScore": "55.000", "m_rewards": "RoyalFavour4", "m_design": ""}, {"id": "67f507c00aba0e02f991ee35", "m_indexer": "lady_frencham_pound:0010", "m_name": "WormscarBasicRoyalOrders", "m_useName": "", "m_index": "0010", "m_mAOrderGiver": "lady_frencham_pound", "m_type": "Repeat", "m_product": "Weapons", "m_blockName": "lady_frencham_pound", "m_collectType": "", "m_tags": "Swords", "m_tagHint": "As long as they look like they're weapons…", "m_cardDescription": "<PERSON>'s despicable uncle, ever on the scrounge, has come begging for alms. She wants some basic weapons to shut him up.", "m_lowQuantity": 4, "m_highQuantity": 20, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "RoyalFavour1", "m_design": ""}, {"id": "67e524e2b3be9302eff9abca", "m_indexer": "macio_gourd:0001", "m_name": "Fancy Outfits", "m_useName": "", "m_index": "0001", "m_mAOrderGiver": "macio_gourd", "m_type": "Repeat", "m_product": "Clothing", "m_blockName": "macio_gourd", "m_collectType": "Male<PERSON><PERSON><PERSON>", "m_tags": "", "m_tagHint": "Collars and belts befit the wearers.", "m_cardDescription": "The pomp and the pageantry. A victory parade will take place in the capital soon, for his Majesty's troops. The honourable Hand wants the court musicians redressed in honour of this occasion.", "m_lowQuantity": 10, "m_highQuantity": 10, "m_lowDesignScore": "0.300", "m_highDesignScore": "0.400", "m_rewards": "RoyalFavour2", "m_design": "1|<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_Mannequin@|0|"}, {"id": "67e42cbec3c86102ddf8dc0c", "m_indexer": "macio_gourd:0010", "m_name": "Female Outfits", "m_useName": "False", "m_index": "0010", "m_mAOrderGiver": "macio_gourd", "m_type": "Repeat", "m_product": "Clothing Female", "m_blockName": "macio_gourd", "m_collectType": "Female<PERSON><PERSON><PERSON>", "m_tags": "", "m_tagHint": "Rags not riches.", "m_cardDescription": "<PERSON><PERSON><PERSON>'s cousin is getting married and she's got five bridesmaids to dress. She needs simple dresses.", "m_lowQuantity": 5, "m_highQuantity": 5, "m_lowDesignScore": "0.010", "m_highDesignScore": "0.200", "m_rewards": "PeopleFavour3", "m_design": "1|<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_Mannequin@|0|"}, {"id": "67e522d9c3c86102ddfa0a89", "m_indexer": "macio_gourd:0020", "m_name": "Flamboyant Male Outfits", "m_useName": "False", "m_index": "0020", "m_mAOrderGiver": "macio_gourd", "m_type": "Repeat", "m_product": "Clothing", "m_blockName": "macio_gourd", "m_collectType": "Male<PERSON><PERSON><PERSON>", "m_tags": "Stylish", "m_tagHint": "Some thing flamboyant for party goers", "m_cardDescription": "When you're an old money member of the continental aristocracy, as <PERSON><PERSON> is, you dress the part and so does your team. <PERSON><PERSON> needs clothes for his entourage.", "m_lowQuantity": 14, "m_highQuantity": 14, "m_lowDesignScore": "0.200", "m_highDesignScore": "0.300", "m_rewards": "LordsFavour3", "m_design": "1|<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_Mannequin@|0|"}, {"id": "673334da2b946e0300f67719", "m_indexer": "QuestOrder:0090", "m_name": "Quest Cotters Food", "m_useName": "", "m_index": "0090", "m_mAOrderGiver": "thomas_cotter", "m_type": "OneShot", "m_product": "Food", "m_blockName": "QuestOrder", "m_collectType": "", "m_tags": "Pie|Poultry", "m_tagHint": "A Pie with chicken", "m_cardDescription": "<PERSON> loves a paltry pie, especially if it involves poultry. And pie. She's a woman of simple tastes.\n<PERSON> needs two chicken pies.\n<Hint:> Bk, bk, bk, b-kwark!", "m_lowQuantity": 2, "m_highQuantity": 2, "m_lowDesignScore": "0.200", "m_highDesignScore": "0.200", "m_rewards": "PeopleFavour2", "m_design": ""}, {"id": "67dc4dabe981f702f7567fbe", "m_indexer": "QuestOrder:0100", "m_name": "<PERSON>'s Sword Order", "m_useName": "", "m_index": "0100", "m_mAOrderGiver": "<PERSON>", "m_type": "OneShot", "m_product": "Weapons", "m_blockName": "QuestOrder", "m_collectType": "", "m_tags": "", "m_tagHint": "", "m_cardDescription": "<PERSON>'s not going in that cave unarmed. \nHe wants a weapon he can kill with.\n<Hint:> A short blade is great for cave fighting.", "m_lowQuantity": 1, "m_highQuantity": 1, "m_lowDesignScore": "0.200", "m_highDesignScore": "0.200", "m_rewards": "LordsFavour2", "m_design": ""}, {"id": "68690f75caef6602d1e91b63", "m_indexer": "rod_smallwood:0010", "m_name": "Pies a plenty", "m_useName": "", "m_index": "0010", "m_mAOrderGiver": "rod_smallwood", "m_type": "Repeat", "m_product": "Food", "m_blockName": "rod_smallwood", "m_collectType": "", "m_tags": "Rat|Pie|Animal_Product|Meat|Cheap", "m_tagHint": "Cheap meat pie things, whatever the meat.", "m_cardDescription": "He needs pies for his chain of street vendors, but they need to be cheep. He does not really care about where the ingredients come from. ", "m_lowQuantity": 10, "m_highQuantity": 12, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "PeopleFavour3", "m_design": ""}, {"id": "686fe8653a44af02deec0750", "m_indexer": "rod_smallwood:0020", "m_name": "I like veg", "m_useName": "", "m_index": "0020", "m_mAOrderGiver": "rod_smallwood", "m_type": "Repeat", "m_product": "Food", "m_blockName": "rod_smallwood", "m_collectType": "", "m_tags": "Fruit|Salad|Healthy|Vegan|Vegitarian", "m_tagHint": "All of your 5 a day.", "m_cardDescription": "<PERSON> and his extended family on on a health drive, supply him with healthy stuff", "m_lowQuantity": 8, "m_highQuantity": 12, "m_lowDesignScore": "0.200", "m_highDesignScore": "0.200", "m_rewards": "PeopleFavour3", "m_design": ""}, {"id": "666983f31ce9fa0027404bd1", "m_indexer": "shoporder:0010", "m_name": "Male Clothing", "m_useName": "True", "m_index": "0010", "m_mAOrderGiver": "Player", "m_type": "Repeat", "m_product": "Clothing", "m_blockName": "shoporder", "m_collectType": "Male<PERSON><PERSON><PERSON>", "m_tags": "", "m_tagHint": "", "m_cardDescription": "Make Clothing For A Male", "m_lowQuantity": 1, "m_highQuantity": 1, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "", "m_design": "1|<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_Mannequin@|0|"}, {"id": "6864ffccf21a5b02e560c47a", "m_indexer": "shoporder:0020", "m_name": "Female Clothing", "m_useName": "True", "m_index": "0020", "m_mAOrderGiver": "Player", "m_type": "Repeat", "m_product": "Clothing Female", "m_blockName": "shoporder", "m_collectType": "Female<PERSON><PERSON><PERSON>", "m_tags": "", "m_tagHint": "", "m_cardDescription": "Make Clothing For A Female", "m_lowQuantity": 1, "m_highQuantity": 1, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "", "m_design": "1|<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_Mannequin@|0|"}, {"id": "65fd6a9e78d13c002ab81f7c", "m_indexer": "silvia_plinth:0010", "m_name": "A Step Beyond Slop", "m_useName": "", "m_index": "0010", "m_mAOrderGiver": "silvia_plinth", "m_type": "Repeat", "m_product": "Food", "m_blockName": "silvia_plinth", "m_collectType": "", "m_tags": "Bread|Baked|Sandwich|Cheap", "m_tagHint": "Bread is filling and easy to produce. Perhaps a sandwich?", "m_cardDescription": "<PERSON><PERSON><PERSON> is a woman on a mission. She always needs cheap food for her brood.", "m_lowQuantity": 3, "m_highQuantity": 3, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "PeopleFavour1", "m_design": ""}, {"id": "65fd6aa81ce3b20028502c8e", "m_indexer": "silvia_plinth:0040", "m_name": "I Can't Believe It's Not Rat", "m_useName": "", "m_index": "0040", "m_mAOrderGiver": "silvia_plinth", "m_type": "Repeat", "m_product": "Food", "m_blockName": "silvia_plinth", "m_collectType": "", "m_tags": "Rat|Animal_Product|Protein|Meat|Unhealthy", "m_tagHint": "Rat meat tastes like chicken.", "m_cardDescription": "<PERSON><PERSON><PERSON> is mobilising her people, sending union agents across the country to spread the word of unity and people power. She wants food that travels to pack in their bags.", "m_lowQuantity": 6, "m_highQuantity": 6, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "PeopleFavour2", "m_design": ""}, {"id": "65e07c4c571e50002874fcac", "m_indexer": "silvia_plinth:0070", "m_name": "<PERSON><PERSON><PERSON>b", "m_useName": "", "m_index": "0070", "m_mAOrderGiver": "silvia_plinth", "m_type": "Repeat", "m_product": "Food", "m_blockName": "silvia_plinth", "m_collectType": "", "m_tags": "Animal_Product|Protein|Meat|Healthy", "m_tagHint": "Healthy Meat, no Carbs", "m_cardDescription": "<PERSON><PERSON><PERSON>'s team has grown. They will gather in her home to thrash out their manifesto. Success will depend on the fullness of the stomachs. She wants food she can serve to impress.", "m_lowQuantity": 8, "m_highQuantity": 8, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "UpgradeWorkerCapacityFactory|PeopleFavour2", "m_design": ""}, {"id": "65fd6aaca3b2e20027c0611a", "m_indexer": "silvia_plinth:0080", "m_name": "Gruel to be Kind", "m_useName": "", "m_index": "0080", "m_mAOrderGiver": "silvia_plinth", "m_type": "Repeat", "m_product": "Food", "m_blockName": "silvia_plinth", "m_collectType": "", "m_tags": "Plant_Based|Salad|Healthy|Vegan|Vegitarian", "m_tagHint": "Avoid meat for vegans", "m_cardDescription": "<PERSON><PERSON><PERSON>'s reputation with the workers of Albion is rising. But she will not let it go to her head. The first meeting of the Union takes place this weekend, she needs to feed everyone who comes.", "m_lowQuantity": 12, "m_highQuantity": 12, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "PeopleFavour3", "m_design": ""}, {"id": "65df4dea85cf300037318cd8", "m_indexer": "Special:0010", "m_name": "First Royal Order", "m_useName": "", "m_index": "0010", "m_mAOrderGiver": "the_honourable_hand", "m_type": "Repeat", "m_product": "Weapons", "m_blockName": "Special", "m_collectType": "", "m_tags": "Swords", "m_tagHint": "Stabby, stabby.", "m_cardDescription": "His Majesty requires [quantity] [quality] swords. Heads will roll if you fail.", "m_lowQuantity": 20, "m_highQuantity": 20, "m_lowDesignScore": "0.200", "m_highDesignScore": "0.200", "m_rewards": "RoyalFavour3", "m_design": ""}, {"id": "67602f4055dd3302fa7220d4", "m_indexer": "Special:0020", "m_name": "BigOrder", "m_useName": "", "m_index": "0020", "m_mAOrderGiver": "silvia_plinth", "m_type": "Repeat", "m_product": "Food", "m_blockName": "Special", "m_collectType": "", "m_tags": "", "m_tagHint": "", "m_cardDescription": "All hands on deck! There's a chance <PERSON><PERSON><PERSON> might convince the dockers that they should join her \"Union\". They're a simple bunch, feed them well, you'll have won have the fight.\nShe wants hearty food for hearts and minds.\n<Hint:> Don't give them fish.", "m_lowQuantity": 300, "m_highQuantity": 400, "m_lowDesignScore": "0.200", "m_highDesignScore": "0.200", "m_rewards": "PeopleFavour5", "m_design": ""}, {"id": "67c9ac708f1a8402f0f79fe9", "m_indexer": "Special:0021", "m_name": "TestImport", "m_useName": "", "m_index": "0021", "m_mAOrderGiver": "silvia_plinth", "m_type": "Repeat", "m_product": "Food", "m_blockName": "Special", "m_collectType": "", "m_tags": "", "m_tagHint": "", "m_cardDescription": "", "m_lowQuantity": "", "m_highQuantity": "", "m_lowDesignScore": "", "m_highDesignScore": "", "m_rewards": "", "m_design": ""}, {"id": "67c6dfd526fd4f0321fbb6de", "m_indexer": "Special:0100", "m_name": "<PERSON>y <PERSON>'s First Big Order", "m_useName": "", "m_index": "0100", "m_mAOrderGiver": "Tricky<PERSON><PERSON><PERSON><PERSON>y", "m_type": "Repeat", "m_product": "Food", "m_blockName": "Special", "m_collectType": "", "m_tags": "Pie", "m_tagHint": "Pies filled with anything", "m_cardDescription": "Lord <PERSON><PERSON><PERSON><PERSON><PERSON>'s huge pie order is in. Make sure it's the best quality. Does it want a huge pie making..?", "m_lowQuantity": 25, "m_highQuantity": 30, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "PeopleFavour4", "m_design": ""}, {"id": "67d6f9f9bd2ea2030020c2f7", "m_indexer": "TrickyOrders:0100", "m_name": "TrickysOrders", "m_useName": "", "m_index": "0100", "m_mAOrderGiver": "Tricky<PERSON><PERSON><PERSON><PERSON>y", "m_type": "Repeat", "m_product": "Food", "m_blockName": "TrickyOrders", "m_collectType": "", "m_tags": "Pie|Plant_Based", "m_tagHint": "Pies for leaf chewers", "m_cardDescription": "Lord <PERSON><PERSON><PERSON><PERSON><PERSON> wants a pie. He wants a pie that tastes of summer and joy and \"the forlorn nature of beauty on the wane\".", "m_lowQuantity": 50, "m_highQuantity": 150, "m_lowDesignScore": "0.200", "m_highDesignScore": "0.400", "m_rewards": "PeopleFavour3", "m_design": ""}, {"id": "67d805645ac625030eb39ff1", "m_indexer": "TrickyOrders:0110", "m_name": "TrickysSpecialOrder", "m_useName": "", "m_index": "0110", "m_mAOrderGiver": "Tricky<PERSON><PERSON><PERSON><PERSON>y", "m_type": "Repeat", "m_product": "Food", "m_blockName": "TrickyOrders", "m_collectType": "", "m_tags": "Carbohydrate|Dairy|Fruit", "m_tagHint": "Think happy foods…", "m_cardDescription": "You know when you step out of your house and into the world and it greets you with a smile and a handshake? Yeah, <PERSON><PERSON> wants something that tastes like that.", "m_lowQuantity": 5, "m_highQuantity": 15, "m_lowDesignScore": "0.600", "m_highDesignScore": "0.600", "m_rewards": "PeopleFavour2", "m_design": ""}, {"id": "666194531703c60028a02560", "m_indexer": "TurretOrder:0010", "m_name": "Turret Order", "m_useName": "", "m_index": "0010", "m_mAOrderGiver": "the_honourable_hand", "m_type": "Repeat", "m_product": "Ammo", "m_blockName": "TurretOrder", "m_collectType": "", "m_tags": "", "m_tagHint": "", "m_cardDescription": "", "m_lowQuantity": -1, "m_highQuantity": -1, "m_lowDesignScore": "0.000", "m_highDesignScore": "0.000", "m_rewards": "", "m_design": ""}, {"id": "66c3374cbc1db80288c99b57", "m_indexer": "weaponorder:0010", "m_name": "Weaponsmith order 1", "m_useName": "", "m_index": "0010", "m_mAOrderGiver": "Player", "m_type": "Repeat", "m_product": "Weapons", "m_blockName": "weaponorder", "m_collectType": "", "m_tags": "", "m_tagHint": "Make it deadly.", "m_cardDescription": "", "m_lowQuantity": 1, "m_highQuantity": 1, "m_lowDesignScore": "", "m_highDesignScore": "", "m_rewards": "", "m_design": ""}, {"id": "67e6e167e86d3f02c5c54bd9", "m_indexer": "Weapons:0010", "m_name": "Simple Weapons", "m_useName": "", "m_index": "0010", "m_mAOrderGiver": "lady_frencham_pound", "m_type": "Repeat", "m_product": "Weapons", "m_blockName": "Weapons", "m_collectType": "", "m_tags": "", "m_tagHint": "", "m_cardDescription": "Lady <PERSON>-<PERSON> is a keen hunter. Her preference is deer. Next weekend she will be entertaining a few friends on her country estate and has planned a ride out to hunt down a few stags. Arm her with a suitable weapon.\n<Hint:> The sharper the better when hunting deer.", "m_lowQuantity": 2, "m_highQuantity": 8, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.002", "m_rewards": "RoyalFavour1", "m_design": ""}, {"id": "67ea5addd34e7802ccd063ee", "m_indexer": "Weapons:0020", "m_name": "Flamboyant Weapons", "m_useName": "", "m_index": "0020", "m_mAOrderGiver": "lady_frencham_pound", "m_type": "Repeat", "m_product": "Weapons", "m_blockName": "Weapons", "m_collectType": "", "m_tags": "", "m_tagHint": "", "m_cardDescription": "The summer social calendar is littered with high profile parties and events. <PERSON> will attend them all, as will her entourage. She requires matching dress weapons to act as her motif.\n<Hint:> Flamboyant and fantastical wins over pragmatic and lethal.", "m_lowQuantity": 6, "m_highQuantity": 14, "m_lowDesignScore": "0.001", "m_highDesignScore": "0.001", "m_rewards": "RoyalFavour1", "m_design": ""}, {"id": "67ea5b9ad34e7802ccd069bc", "m_indexer": "Weapons:0030", "m_name": "Fancy Weapons", "m_useName": "", "m_index": "0030", "m_mAOrderGiver": "lady_frencham_pound", "m_type": "Repeat", "m_product": "Weapons", "m_blockName": "Weapons", "m_collectType": "", "m_tags": "", "m_tagHint": "", "m_cardDescription": "The jousting tournaments are the real reason <PERSON><PERSON><PERSON><PERSON> attends any social event. It's here that she can exert her dominance over the rest of the aristocracy. She needs to arm her knights with the best weapons money can buy.\n<Hint:> Style is just as important as substance here.", "m_lowQuantity": 10, "m_highQuantity": 20, "m_lowDesignScore": "0.200", "m_highDesignScore": "0.200", "m_rewards": "RoyalFavour2", "m_design": ""}]