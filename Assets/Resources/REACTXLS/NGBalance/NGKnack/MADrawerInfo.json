[{"id": "66d5de088bcd3d02d59ff33e", "m_drawerIndex": "Armour:Arms", "m_number": 3, "m_drawerTitle": "Arms", "m_displayTitle": "Arms", "m_drawerName": "Armour", "m_displayName": "Armour Male", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "66c33a46c46ee502850debbb", "m_drawerIndex": "Armour:Chest", "m_number": 2, "m_drawerTitle": "Chest", "m_displayTitle": "Chest", "m_drawerName": "Armour", "m_displayName": "Armour Male", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "66d5cb5ac3b816030025a111", "m_drawerIndex": "Armourer:Action", "m_number": 1, "m_drawerTitle": "Action", "m_displayTitle": "Action", "m_drawerName": "<PERSON><PERSON><PERSON>", "m_displayName": "<PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "685534173677bb02ec23f213", "m_drawerIndex": "Armourer:Aesthetic", "m_number": 5, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "<PERSON><PERSON><PERSON>", "m_displayName": "<PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "66cdb43b7384b80283779398", "m_drawerIndex": "Armourer:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "<PERSON><PERSON><PERSON>", "m_displayName": "<PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "66d5cb41a10a1802b7d8f4bc", "m_drawerIndex": "Armourer:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "<PERSON><PERSON><PERSON>", "m_displayName": "<PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "66d5cbb70de93b0315b32d5f", "m_drawerIndex": "Armourer:Stock", "m_number": 4, "m_drawerTitle": "Stock", "m_displayTitle": "Stock", "m_drawerName": "<PERSON><PERSON><PERSON>", "m_displayName": "<PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "66d5e04992ca5502d4b983fc", "m_drawerIndex": "Armour:Feet", "m_number": 6, "m_drawerTitle": "Feet", "m_displayTitle": "Feet", "m_drawerName": "Armour", "m_displayName": "Armour Male", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "68810476e3ef2e02e3ddb32d", "m_drawerIndex": "Armour Female:Arms", "m_number": 3, "m_drawerTitle": "Arms", "m_displayTitle": "Arms", "m_drawerName": "Armour Female", "m_displayName": "Armour Female", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "6881044b9390fe0304fd5b25", "m_drawerIndex": "Armour Female:Chest", "m_number": 2, "m_drawerTitle": "Chest", "m_displayTitle": "Chest", "m_drawerName": "Armour Female", "m_displayName": "Armour Female", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "688104be88d2ae02ca2a2a6b", "m_drawerIndex": "Armour Female:Feet", "m_number": 6, "m_drawerTitle": "Feet", "m_displayTitle": "Feet", "m_drawerName": "Armour Female", "m_displayName": "Armour Female", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "688103a7d4a3c602d0e40e41", "m_drawerIndex": "Armour Female:Head", "m_number": 1, "m_drawerTitle": "Head", "m_displayTitle": "Head", "m_drawerName": "Armour Female", "m_displayName": "Armour Female", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "68810488d4a3c602d0e41517", "m_drawerIndex": "Armour Female:Hips", "m_number": 4, "m_drawerTitle": "Hips", "m_displayTitle": "Hips", "m_drawerName": "Armour Female", "m_displayName": "Armour Female", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "688104adc5137002eddbbc4e", "m_drawerIndex": "Armour Female:Legs", "m_number": 5, "m_drawerTitle": "Legs", "m_displayTitle": "Legs", "m_drawerName": "Armour Female", "m_displayName": "Armour Female", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "66d5ddb9756d6302f90089f7", "m_drawerIndex": "Armour:Head", "m_number": 1, "m_drawerTitle": "Head", "m_displayTitle": "Head", "m_drawerName": "Armour", "m_displayName": "Armour Male", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "66d5de296ace8102e4d9afed", "m_drawerIndex": "Armour:Hips", "m_number": 4, "m_drawerTitle": "Hips", "m_displayTitle": "Hips", "m_drawerName": "Armour", "m_displayName": "Armour Male", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "66d5e00ad8b43802c478cfd9", "m_drawerIndex": "Armour:Legs", "m_number": 5, "m_drawerTitle": "Legs", "m_displayTitle": "Legs", "m_drawerName": "Armour", "m_displayName": "Armour Male", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "67bee7bd0ff3e902e0772b1d", "m_drawerIndex": "Beacon :Beacon Parts", "m_number": 1, "m_drawerTitle": "Beacon Parts", "m_displayTitle": "Beacon Parts", "m_drawerName": "Beacon ", "m_displayName": "Beacon ", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": ""}, {"id": "67d021701cd60602fe614708", "m_drawerIndex": "Clothing:Body", "m_number": 2, "m_drawerTitle": "Body", "m_displayTitle": "Body", "m_drawerName": "Clothing", "m_displayName": "Clothing Male", "m_drawerType": "Product", "m_unlockAtStart": "True", "m_district": "Briar Lakes"}, {"id": "67d021761cd60602fe614742", "m_drawerIndex": "Clothing:<PERSON>", "m_number": 4, "m_drawerTitle": "Boots", "m_displayTitle": "Feet", "m_drawerName": "Clothing", "m_displayName": "Clothing Male", "m_drawerType": "Product", "m_unlockAtStart": "True", "m_district": "Briar Lakes"}, {"id": "6881069a88d2ae02ca2a3326", "m_drawerIndex": "Clothing Female:Body", "m_number": 2, "m_drawerTitle": "Body", "m_displayTitle": "Body", "m_drawerName": "Clothing Female", "m_displayName": "Clothing Female", "m_drawerType": "Product", "m_unlockAtStart": "True", "m_district": "Briar Lakes"}, {"id": "688106ee9390fe0304fd60a4", "m_drawerIndex": "Clothing Female:Feet", "m_number": 4, "m_drawerTitle": "Feet", "m_displayTitle": "Feet", "m_drawerName": "Clothing Female", "m_displayName": "Clothing Female", "m_drawerType": "Product", "m_unlockAtStart": "True", "m_district": "Briar Lakes"}, {"id": "6881059bc0eb6202ee432bce", "m_drawerIndex": "Clothing Female:Head", "m_number": 1, "m_drawerTitle": "Head", "m_displayTitle": "Head", "m_drawerName": "Clothing Female", "m_displayName": "Clothing Female", "m_drawerType": "Product", "m_unlockAtStart": "True", "m_district": "Briar Lakes"}, {"id": "688106ab88d2ae02ca2a33b0", "m_drawerIndex": "Clothing Female:<PERSON><PERSON>", "m_number": 3, "m_drawerTitle": "<PERSON>ts", "m_displayTitle": "Legs", "m_drawerName": "Clothing Female", "m_displayName": "Clothing Female", "m_drawerType": "Product", "m_unlockAtStart": "True", "m_district": "Briar Lakes"}, {"id": "67d021031cd60602fe61446c", "m_drawerIndex": "Clothing:Head", "m_number": 1, "m_drawerTitle": "Head", "m_displayTitle": "Head", "m_drawerName": "Clothing", "m_displayName": "Clothing Male", "m_drawerType": "Product", "m_unlockAtStart": "True", "m_district": "Briar Lakes"}, {"id": "67d021731cd60602fe614726", "m_drawerIndex": "Clothing:<PERSON><PERSON>", "m_number": 3, "m_drawerTitle": "<PERSON>ts", "m_displayTitle": "Legs", "m_drawerName": "Clothing", "m_displayName": "Clothing Male", "m_drawerType": "Product", "m_unlockAtStart": "True", "m_district": "Briar Lakes"}, {"id": "65bb94a3f700a4002854c2f9", "m_drawerIndex": "Decorations:Stick<PERSON>", "m_number": 1, "m_drawerTitle": "Stickers", "m_displayTitle": "Stickers", "m_drawerName": "Decorations", "m_displayName": "Decorations", "m_drawerType": "Product, Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "684c312c8713de0309935d2a", "m_drawerIndex": "Dispatch:Action", "m_number": 1, "m_drawerTitle": "Action", "m_displayTitle": "Action", "m_drawerName": "Dispatch", "m_displayName": "Food Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Bandit Camp"}, {"id": "684c33b0c7fa2d02e3868141", "m_drawerIndex": "Dispatch:Aesthetic", "m_number": 5, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "Dispatch", "m_displayName": "Food Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "684c324a9aaf6d02c80313e3", "m_drawerIndex": "Dispatch Cotton:Action", "m_number": 1, "m_drawerTitle": "Action", "m_displayTitle": "Action", "m_drawerName": "Dispatch Cotton", "m_displayName": "<PERSON><PERSON>h Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "684c34653e95720304bdda32", "m_drawerIndex": "Dispatch Cotton:Aesthetic", "m_number": 5, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "Dispatch Cotton", "m_displayName": "<PERSON><PERSON>h Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "67ed3f2c49ee0502d7e6432c", "m_drawerIndex": "Dispatch Cotton:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "Dispatch Cotton", "m_displayName": "<PERSON><PERSON>h Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "67ed3f199921c902e2ad8c01", "m_drawerIndex": "Dispatch Cotton:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "Dispatch Cotton", "m_displayName": "<PERSON><PERSON>h Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "67ed3f31971d6d02ed895a63", "m_drawerIndex": "Dispatch Cotton:Stock", "m_number": 4, "m_drawerTitle": "Stock", "m_displayTitle": "Stock", "m_drawerName": "Dispatch Cotton", "m_displayName": "<PERSON><PERSON>h Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "67360a46b2844002da42199e", "m_drawerIndex": "Dispatch:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "Dispatch", "m_displayName": "Food Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "684c3272ede8550302225a22", "m_drawerIndex": "Dispatch Metal:Action", "m_number": 1, "m_drawerTitle": "Action", "m_displayTitle": "Action", "m_drawerName": "Dispatch Metal", "m_displayName": "Metal Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Road to Wyrmscar"}, {"id": "684c3411d452f502ee148db9", "m_drawerIndex": "Dispatch Metal:Aesthetic", "m_number": 5, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "Dispatch Metal", "m_displayName": "Metal Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Road to Wyrmscar"}, {"id": "6761581968f40c02f046017d", "m_drawerIndex": "Dispatch Metal:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "Dispatch Metal", "m_displayName": "Metal Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Road to Wyrmscar"}, {"id": "676158b97ff03902de36c3b8", "m_drawerIndex": "Dispatch Metal:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "Dispatch Metal", "m_displayName": "Metal Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Road to Wyrmscar"}, {"id": "678903336b2f4502b58c309e", "m_drawerIndex": "Dispatch Metal:Stock", "m_number": 4, "m_drawerTitle": "Stock", "m_displayTitle": "Stock", "m_drawerName": "Dispatch Metal", "m_displayName": "Metal Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Road to Wyrmscar"}, {"id": "673609f76cfacb030a9ca8c0", "m_drawerIndex": "Dispatch:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "Dispatch", "m_displayName": "Food Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "67360a7f75689202af920bc7", "m_drawerIndex": "Dispatch:Stock", "m_number": 4, "m_drawerTitle": "Stock", "m_displayTitle": "Stock", "m_drawerName": "Dispatch", "m_displayName": "Food Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "684c328bede8550302225aa7", "m_drawerIndex": "Dispatch Swamp :Action", "m_number": 1, "m_drawerTitle": "Action", "m_displayTitle": "Action", "m_drawerName": "Dispatch Swamp ", "m_displayName": "Wood Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Bandit Camp"}, {"id": "684c3444d452f502ee148e2a", "m_drawerIndex": "Dispatch Swamp :Aesthetic", "m_number": 5, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "Dispatch Swamp ", "m_displayName": "Wood Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Bandit Camp"}, {"id": "678903baefac9e030ed85bf9", "m_drawerIndex": "Dispatch Swamp :Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "Dispatch Swamp ", "m_displayName": "Wood Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Bandit Camp"}, {"id": "678903b5efac9e030ed85bed", "m_drawerIndex": "Dispatch Swamp :Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "Dispatch Swamp ", "m_displayName": "Wood Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Bandit Camp"}, {"id": "678903bcac9ca502fb1cae44", "m_drawerIndex": "Dispatch Swamp :Stock", "m_number": 4, "m_drawerTitle": "Stock", "m_displayTitle": "Stock", "m_drawerName": "Dispatch Swamp ", "m_displayName": "Wood Dispatch", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Bandit Camp"}, {"id": "649c4936d42ae5002a187bea", "m_drawerIndex": "Factory:Aesthetic", "m_number": 7, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "Factory", "m_displayName": "Factory", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "649c3b6113f475002aac8d0e", "m_drawerIndex": "Factory:Entrances", "m_number": 4, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "Factory", "m_displayName": "Factory", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "64b66721ca3be3002a387508", "m_drawerIndex": "Factory:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "Factory", "m_displayName": "Factory", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "649c3bfd83b90d002936ef9d", "m_drawerIndex": "Factory:Stock", "m_number": 5, "m_drawerTitle": "Stock", "m_displayTitle": "Stock", "m_drawerName": "Factory", "m_displayName": "Factory", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes|Oakridge"}, {"id": "649c3b589238940027a7c339", "m_drawerIndex": "Factory:Workstation", "m_number": 1, "m_drawerTitle": "Workstation", "m_displayTitle": "Workstation", "m_drawerName": "Factory", "m_displayName": "Factory", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "64a3eaafaaf4f60028429459", "m_drawerIndex": "Farm:Aesthetic", "m_number": 7, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "Farm", "m_displayName": "Food Farm", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "685139e33e95720304c307dd", "m_drawerIndex": "Farm Cotton:Aesthetic", "m_number": 6, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "Farm Cotton", "m_displayName": "Cloth Farm", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "67b857ae5852e502da873e95", "m_drawerIndex": "Farm Cotton:Entrances", "m_number": 4, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "Farm Cotton", "m_displayName": "Cloth Farm", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "67b857ab34bf3d02c49d8835", "m_drawerIndex": "Farm Cotton:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "Farm Cotton", "m_displayName": "Cloth Farm", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "685139d23e95720304c30755", "m_drawerIndex": "Farm Cotton:Stock", "m_number": 5, "m_drawerTitle": "Stock", "m_displayTitle": "Stock", "m_drawerName": "Farm Cotton", "m_displayName": "Cloth Farm", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "67b857b00a2efe02f8473fd5", "m_drawerIndex": "Farm Cotton:Workstation", "m_number": 1, "m_drawerTitle": "Workstation", "m_displayTitle": "Workstation", "m_drawerName": "Farm Cotton", "m_displayName": "Cloth Farm", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "649c3a21d42ae5002a17f8dc", "m_drawerIndex": "Farm:Entrances", "m_number": 4, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "Farm", "m_displayName": "Food Farm", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "649c3a8382ba2a00273e3016", "m_drawerIndex": "Farm:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "Farm", "m_displayName": "Food Farm", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "649c3a9a9238940027a7bfa2", "m_drawerIndex": "Farm:Stock", "m_number": 5, "m_drawerTitle": "Stock", "m_displayTitle": "Stock", "m_drawerName": "Farm", "m_displayName": "Food Farm", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "64901f9bdf6f1e0027eb52b8", "m_drawerIndex": "Farm:Workstation", "m_number": 1, "m_drawerTitle": "Workstation", "m_displayTitle": "Workstation", "m_drawerName": "Farm", "m_displayName": "Food Farm", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "6499606a92389400278f6d3d", "m_drawerIndex": "Food:Bread", "m_number": 1, "m_drawerTitle": "Bread", "m_displayTitle": "Bread", "m_drawerName": "Food", "m_displayName": "Food", "m_drawerType": "Product", "m_unlockAtStart": "True", "m_district": "Oakridge"}, {"id": "6887a340c78a5d02c424056b", "m_drawerIndex": "Food:Cakes", "m_number": 5, "m_drawerTitle": "Cakes", "m_displayTitle": "Cakes", "m_drawerName": "Food", "m_displayName": "Food", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": ""}, {"id": "65bbabfd7cf692002618f7de", "m_drawerIndex": "Food:Cutlery", "m_number": 7, "m_drawerTitle": "Cutlery", "m_displayTitle": "Cutlery", "m_drawerName": "Food", "m_displayName": "Food", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "649984aea9d9270026a2abbc", "m_drawerIndex": "Food:Dairy", "m_number": 4, "m_drawerTitle": "Dairy", "m_displayTitle": "Dairy", "m_drawerName": "Food", "m_displayName": "Food", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "65bbaf5973300f00273b5d4c", "m_drawerIndex": "Food:Fillings", "m_number": 2, "m_drawerTitle": "Fillings", "m_displayTitle": "Fillings", "m_drawerName": "Food", "m_displayName": "Food", "m_drawerType": "Product", "m_unlockAtStart": "True", "m_district": "Oakridge"}, {"id": "663355c19d06d50028ff115c", "m_drawerIndex": "Food:Pies & Soups", "m_number": 3, "m_drawerTitle": "Pies & Soups", "m_displayTitle": "Pies & Soups", "m_drawerName": "Food", "m_displayName": "Food", "m_drawerType": "Product", "m_unlockAtStart": "True", "m_district": "Oakridge"}, {"id": "67289c5f426d54030d6184fa", "m_drawerIndex": "Heroes Guild:Action", "m_number": 1, "m_drawerTitle": "Action", "m_displayTitle": "Action", "m_drawerName": "Heroes Guild", "m_displayName": "Heroes Guild", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "670e8bc29786f202f6ea5d5e", "m_drawerIndex": "Heroes Guild:Bedrooms", "m_number": 3, "m_drawerTitle": "Bedrooms", "m_displayTitle": "Bedrooms", "m_drawerName": "Heroes Guild", "m_displayName": "Heroes Guild", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "670e8baa02e48a02d3e8f548", "m_drawerIndex": "Heroes Guild:Entrances", "m_number": 4, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "Heroes Guild", "m_displayName": "Heroes Guild", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "67220b10900d9702f5acc37a", "m_drawerIndex": "Heroes Guild:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "Heroes Guild", "m_displayName": "Heroes Guild", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "685020473e95720304c148a7", "m_drawerIndex": "House:Aesthetic", "m_number": 4, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "House", "m_displayName": "Food House", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "685423a4230a9402c894b985", "m_drawerIndex": "House:Bedrooms", "m_number": 2, "m_drawerTitle": "Bedrooms", "m_displayTitle": "Bedrooms", "m_drawerName": "House", "m_displayName": "Food House", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge|The Cotterswolds"}, {"id": "685024f8ba34f102e140cc56", "m_drawerIndex": "House Cotton:Aesthetic", "m_number": 4, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "House Cotton", "m_displayName": "Cloth House", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "685024bcc0cb3e02f97005b9", "m_drawerIndex": "House Cotton:Bedrooms", "m_number": 2, "m_drawerTitle": "Bedrooms", "m_displayTitle": "Bedrooms", "m_drawerName": "House Cotton", "m_displayName": "Cloth House", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "685024e4c0cb3e02f9700678", "m_drawerIndex": "House Cotton:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "House Cotton", "m_displayName": "Cloth House", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "685022f0cebfc702d696f84a", "m_drawerIndex": "House Cotton:Roofs", "m_number": 1, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "House Cotton", "m_displayName": "Cloth House", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "685423883677bb02ec22cf57", "m_drawerIndex": "House:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "House", "m_displayName": "Food House", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "6850270c3e95720304c1624c", "m_drawerIndex": "House Metal:Aesthetic", "m_number": 4, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "House Metal", "m_displayName": "Metal House", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "685026bcd452f502ee17af6b", "m_drawerIndex": "House Metal:Bedrooms", "m_number": 2, "m_drawerTitle": "Bedrooms", "m_displayTitle": "Bedrooms", "m_drawerName": "House Metal", "m_displayName": "Metal House", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "685026dffa796e02ca74a70b", "m_drawerIndex": "House Metal:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "House Metal", "m_displayName": "Metal House", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "68502675adc14f02f723f524", "m_drawerIndex": "House Metal:Roofs", "m_number": 1, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "House Metal", "m_displayName": "Metal House", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "64999fa57525690027437042", "m_drawerIndex": "House:Roofs", "m_number": 1, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "House", "m_displayName": "Food House", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "The Cotterswolds"}, {"id": "68502e8bfa796e02ca74c1f6", "m_drawerIndex": "House: Swamp:Aesthetic", "m_number": 4, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "House: Swamp", "m_displayName": "Wood House", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Bandit Camp"}, {"id": "6787d6666d2c7f02cb83350d", "m_drawerIndex": "House: Swamp:Bedrooms", "m_number": 2, "m_drawerTitle": "Bedrooms", "m_displayTitle": "Bedrooms", "m_drawerName": "House: Swamp", "m_displayName": "Wood House", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Bandit Camp"}, {"id": "6787d64dac9ca502fb1aef7d", "m_drawerIndex": "House: Swamp:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "House: Swamp", "m_displayName": "Wood House", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Bandit Camp"}, {"id": "6787d606efac9e030ed64ec9", "m_drawerIndex": "House: Swamp:Roofs", "m_number": 1, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "House: Swamp", "m_displayName": "Wood House", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Bandit Camp"}, {"id": "67ba430e71c9e50f7d06e381", "m_drawerIndex": "Lumber Mill:Action", "m_number": 1, "m_drawerTitle": "Action", "m_displayTitle": "Action", "m_drawerName": "Lumber Mill", "m_displayName": "Lumber Mill", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Bandit Camp"}, {"id": "662035070acd2e0027bc6561", "m_drawerIndex": "Lumber Mill:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "Lumber Mill", "m_displayName": "Lumber Mill", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Bandit Camp"}, {"id": "66203527de3465002790264c", "m_drawerIndex": "Lumber Mill:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "Lumber Mill", "m_displayName": "Lumber Mill", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Bandit Camp"}, {"id": "6674255c8285f70027ce2450", "m_drawerIndex": "Lumber Mill:Stock", "m_number": 4, "m_drawerTitle": "Stock", "m_displayTitle": "Stock", "m_drawerName": "Lumber Mill", "m_displayName": "Lumber Mill", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Bandit Camp"}, {"id": "67efa2c56abaee02fc646726", "m_drawerIndex": "Metal Mine:Aesthetic", "m_number": 6, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "Metal Mine", "m_displayName": "Metal Mine", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "65f838967094c5002881212f", "m_drawerIndex": "Metal Mine:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "Metal Mine", "m_displayName": "Metal Mine", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "65f83979542332002802330b", "m_drawerIndex": "Metal Mine:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "Metal Mine", "m_displayName": "Metal Mine", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "65f83920b5000000276faea9", "m_drawerIndex": "Metal Mine:Stock", "m_number": 5, "m_drawerTitle": "Stock", "m_displayTitle": "Stock", "m_drawerName": "Metal Mine", "m_displayName": "Metal Mine", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "680766225147a302c7e986af", "m_drawerIndex": "Metal Mine:Workstation", "m_number": 1, "m_drawerTitle": "Workstation", "m_displayTitle": "Workstation", "m_drawerName": "Metal Mine", "m_displayName": "Metal Mine", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "67eeb23b6abaee02fc63a66d", "m_drawerIndex": "Metal Smelter:Aesthetic", "m_number": 6, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "Metal Smelter", "m_displayName": "Metal Smelter", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "65f83e6d18f2e80027468655", "m_drawerIndex": "Metal Smelter:Entrances", "m_number": 4, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "Metal Smelter", "m_displayName": "Metal Smelter", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "65f83f1c67df500027063f5f", "m_drawerIndex": "Metal Smelter:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "Metal Smelter", "m_displayName": "Metal Smelter", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "65f83e9b3ce1140025253efe", "m_drawerIndex": "Metal Smelter:Stock", "m_number": 5, "m_drawerTitle": "Stock", "m_displayTitle": "Stock", "m_drawerName": "Metal Smelter", "m_displayName": "Metal Smelter", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "680b4e224c8b3202c288808a", "m_drawerIndex": "Metal Smelter:Workstation", "m_number": 1, "m_drawerTitle": "Workstation", "m_displayTitle": "Workstation", "m_drawerName": "Metal Smelter", "m_displayName": "Metal Smelter", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "6851409a8713de0309986c6d", "m_drawerIndex": "Mill Cotton:Aesthetic", "m_number": 6, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "Mill Cotton", "m_displayName": "Cloth Mill", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "683dd47c0d5bc3030ef2c0fa", "m_drawerIndex": "Mill Cotton:Entrances", "m_number": 4, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "Mill Cotton", "m_displayName": "Cloth Mill", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "683dd43f3fd1d602eb63735d", "m_drawerIndex": "Mill Cotton:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "Mill Cotton", "m_displayName": "Cloth Mill", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "683dd49698ed9002dbd4752b", "m_drawerIndex": "Mill Cotton:Stock", "m_number": 5, "m_drawerTitle": "Stock", "m_displayTitle": "Stock", "m_drawerName": "Mill Cotton", "m_displayName": "Cloth Mill", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "683dd40f3fd1d602eb637284", "m_drawerIndex": "Mill Cotton:Workstation", "m_number": 1, "m_drawerTitle": "Workstation", "m_displayTitle": "Workstation", "m_drawerName": "Mill Cotton", "m_displayName": "Cloth Mill", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "68513af03e95720304c30edd", "m_drawerIndex": "Mill Produce:Aesthetic", "m_number": 6, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "Mill Produce", "m_displayName": "Food Mill", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "649af3b113f475002aa1802f", "m_drawerIndex": "Mill Produce:Entrances", "m_number": 4, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "Mill Produce", "m_displayName": "Food Mill", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "649af3e075256900275066db", "m_drawerIndex": "Mill Produce:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "Mill Produce", "m_displayName": "Food Mill", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "649af3dc5ff0510028a3ff78", "m_drawerIndex": "Mill Produce:Stock", "m_number": 5, "m_drawerTitle": "Stock", "m_displayTitle": "Stock", "m_drawerName": "Mill Produce", "m_displayName": "Food Mill", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "649c491c5c28330027644836", "m_drawerIndex": "Mill Produce:Workstation", "m_number": 1, "m_drawerTitle": "Workstation", "m_displayTitle": "Workstation", "m_drawerName": "Mill Produce", "m_displayName": "Food Mill", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "6516ab85a6a11700280f9dda", "m_drawerIndex": "Monsters:New Blocks", "m_number": 1, "m_drawerTitle": "New Blocks", "m_displayTitle": "New Blocks", "m_drawerName": "Monsters", "m_displayName": "Monsters", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "661d3d280c4d67002868157d", "m_drawerIndex": "Shop:Action", "m_number": 1, "m_drawerTitle": "Action", "m_displayTitle": "Action", "m_drawerName": "Shop", "m_displayName": "Clothes Shop", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "6863ae94ead71f02dd161408", "m_drawerIndex": "Shop:Aesthetic", "m_number": 5, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "Shop", "m_displayName": "Clothes Shop", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "66d5cdd7f3c4dd03094da791", "m_drawerIndex": "Shop:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "Shop", "m_displayName": "Clothes Shop", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "661d136df8826100273567dc", "m_drawerIndex": "Shop:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "Shop", "m_displayName": "Clothes Shop", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "661d1386fc0230002969ed7e", "m_drawerIndex": "Shop:Stock", "m_number": 4, "m_drawerTitle": "Stock", "m_displayTitle": "Stock", "m_drawerName": "Shop", "m_displayName": "Clothes Shop", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "684c0f27adc14f02f71f76f9", "m_drawerIndex": "Tavern:Action", "m_number": 1, "m_drawerTitle": "Action", "m_displayTitle": "Action", "m_drawerName": "Tavern", "m_displayName": "Food Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes|Oakridge"}, {"id": "684c1044c7fa2d02e3861d7a", "m_drawerIndex": "Tavern:Aesthetic", "m_number": 4, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "Tavern", "m_displayName": "Food Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "684af5434a221d02c5622d1e", "m_drawerIndex": "Tavern Cotton:Action", "m_number": 1, "m_drawerTitle": "Action", "m_displayTitle": "Action", "m_drawerName": "Tavern Cotton", "m_displayName": "Cloth Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "684afd18f39c8b02d4b1e419", "m_drawerIndex": "Tavern Cotton:Aesthetic", "m_number": 4, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "Tavern Cotton", "m_displayName": "Cloth Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "67d4640ddd9850032a1af014", "m_drawerIndex": "Tavern Cotton:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "Tavern Cotton", "m_displayName": "Cloth Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "684af5b117239d030c41c06d", "m_drawerIndex": "Tavern Cotton:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "Tavern Cotton", "m_displayName": "Cloth Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Briar Lakes"}, {"id": "64a548ff56b25e002936c9d9", "m_drawerIndex": "Tavern:Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "Tavern", "m_displayName": "Food Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "684c100bc7fa2d02e3861b25", "m_drawerIndex": "Tavern Metal :Action", "m_number": 1, "m_drawerTitle": "Action", "m_displayTitle": "Action", "m_drawerName": "Tavern Metal ", "m_displayName": "Metal Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "67daa55a1eccf702ceadcded", "m_drawerIndex": "Tavern Metal :Aesthetic", "m_number": 4, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "Tavern Metal ", "m_displayName": "Metal Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "67daa54db9d75c0326e462e6", "m_drawerIndex": "Tavern Metal :Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "Tavern Metal ", "m_displayName": "Metal Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "67daa566035233032af1b1b4", "m_drawerIndex": "Tavern Metal :Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "Tavern Metal ", "m_displayName": "Metal Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "64a5490adcb91a00287e8bfd", "m_drawerIndex": "Tavern:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "Tavern", "m_displayName": "Food Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "67853d7d6b656302d37e5eb7", "m_drawerIndex": "Tavern Swamp :Action", "m_number": 1, "m_drawerTitle": "Action", "m_displayTitle": "Action", "m_drawerName": "Tavern Swamp ", "m_displayName": "Wood Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Bandit Camp"}, {"id": "684c110bc0cb3e02f96c92f8", "m_drawerIndex": "Tavern Swamp :Aesthetic", "m_number": 4, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "Tavern Swamp ", "m_displayName": "Wood Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Bandit Camp"}, {"id": "684c10f6cebfc702d6927c13", "m_drawerIndex": "Tavern Swamp :Entrances", "m_number": 3, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "Tavern Swamp ", "m_displayName": "Wood Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Bandit Camp"}, {"id": "684c10e5d452f502ee142508", "m_drawerIndex": "Tavern Swamp :Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "Tavern Swamp ", "m_displayName": "Wood Tavern", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Bandit Camp"}, {"id": "661e65bbf018b200294a9024", "m_drawerIndex": "Turret:Action", "m_number": 1, "m_drawerTitle": "Action", "m_displayTitle": "Action", "m_drawerName": "<PERSON><PERSON><PERSON>", "m_displayName": "<PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Road to Wyrmscar"}, {"id": "684926382e053d02df75ab9f", "m_drawerIndex": "Turret:<PERSON><PERSON>", "m_number": 5, "m_drawerTitle": "Ammo", "m_displayTitle": "Ammo", "m_drawerName": "<PERSON><PERSON><PERSON>", "m_displayName": "<PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "682c855812b72c02f558a798", "m_drawerIndex": "Turret:<PERSON><PERSON>", "m_number": 4, "m_drawerTitle": "Ammo Pad", "m_displayTitle": "Ammo Pad", "m_drawerName": "<PERSON><PERSON><PERSON>", "m_displayName": "<PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Oakridge"}, {"id": "661e6619866a450028878605", "m_drawerIndex": "Turret:Bases", "m_number": 2, "m_drawerTitle": "Bases", "m_displayTitle": "Bases", "m_drawerName": "<PERSON><PERSON><PERSON>", "m_displayName": "<PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Road to Wyrmscar"}, {"id": "6812515bab75f002f65cffbd", "m_drawerIndex": "Turret:Shafts", "m_number": 3, "m_drawerTitle": "Shafts", "m_displayTitle": "Shafts", "m_drawerName": "<PERSON><PERSON><PERSON>", "m_displayName": "<PERSON><PERSON><PERSON>", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": "Road to Wyrmscar"}, {"id": "64e70bcab34a18002744f170", "m_drawerIndex": "Weapons:Axes", "m_number": 2, "m_drawerTitle": "Axes", "m_displayTitle": "Axes", "m_drawerName": "Weapons", "m_displayName": "Weapons", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "64e70b66f017f100293b8041", "m_drawerIndex": "Weapons:Blades", "m_number": 1, "m_drawerTitle": "Blades", "m_displayTitle": "Blades", "m_drawerName": "Weapons", "m_displayName": "Weapons", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "64e70b610e263b002736c381", "m_drawerIndex": "Weapons:Crossguards", "m_number": 5, "m_drawerTitle": "Crossguards", "m_displayTitle": "Crossguards", "m_drawerName": "Weapons", "m_displayName": "Weapons", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "64e70b5b4c69d600285d2301", "m_drawerIndex": "Weapons:Grips", "m_number": 7, "m_drawerTitle": "Grips", "m_displayTitle": "Grips", "m_drawerName": "Weapons", "m_displayName": "Weapons", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "66ded193973ca102f0cad663", "m_drawerIndex": "Weapons:Hammers", "m_number": 4, "m_drawerTitle": "Hammers", "m_displayTitle": "Hammers", "m_drawerName": "Weapons", "m_displayName": "Weapons", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "66bb475bc1674d0031f87540", "m_drawerIndex": "Weapons:Maces", "m_number": 3, "m_drawerTitle": "Maces", "m_displayTitle": "Maces", "m_drawerName": "Weapons", "m_displayName": "Weapons", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "67fabe2e7577bf02d5e8f0bc", "m_drawerIndex": "Weaponsmith:Action", "m_number": 1, "m_drawerTitle": "Action", "m_displayTitle": "Action", "m_drawerName": "Weaponsmith", "m_displayName": "Weaponsmith", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": ""}, {"id": "66d5d81e6ace8102e4d98974", "m_drawerIndex": "Weaponsmith:Aesthetic", "m_number": 3, "m_drawerTitle": "Aesthetic", "m_displayTitle": "Aesthetic", "m_drawerName": "Weaponsmith", "m_displayName": "Weaponsmith", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": ""}, {"id": "66bf3182ddcc9e0287443b7c", "m_drawerIndex": "Weaponsmith:Entrances", "m_number": 4, "m_drawerTitle": "Entrances", "m_displayTitle": "Entrances", "m_drawerName": "Weaponsmith", "m_displayName": "Weaponsmith", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": ""}, {"id": "66d5d7a628df1f02fcac396b", "m_drawerIndex": "Weaponsmith:Roofs", "m_number": 2, "m_drawerTitle": "Roofs", "m_displayTitle": "Roofs", "m_drawerName": "Weaponsmith", "m_displayName": "Weaponsmith", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": ""}, {"id": "66d5d9f75c1bd702db58b52f", "m_drawerIndex": "Weaponsmith:Stock", "m_number": 5, "m_drawerTitle": "Stock", "m_displayTitle": "Stock", "m_drawerName": "Weaponsmith", "m_displayName": "Weaponsmith", "m_drawerType": "Building", "m_unlockAtStart": "False", "m_district": ""}, {"id": "65968d478477e90027687568", "m_drawerIndex": "Weapons:<PERSON><PERSON><PERSON>", "m_number": 8, "m_drawerTitle": "<PERSON><PERSON><PERSON>", "m_displayTitle": "<PERSON><PERSON><PERSON>", "m_drawerName": "Weapons", "m_displayName": "Weapons", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}, {"id": "66bb710ec1674d0031f927b6", "m_drawerIndex": "Weapons:Shafts", "m_number": 6, "m_drawerTitle": "Shafts", "m_displayTitle": "Shafts", "m_drawerName": "Weapons", "m_displayName": "Weapons", "m_drawerType": "Product", "m_unlockAtStart": "False", "m_district": "Wyrmscar"}]