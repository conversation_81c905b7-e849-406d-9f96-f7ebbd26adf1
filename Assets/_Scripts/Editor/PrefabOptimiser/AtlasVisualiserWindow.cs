//#define HOVER_RECT

using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;
using Unity.Collections;
using Unity.Jobs;
using Unity.Mathematics;

public class AtlasVisualiserWindow : EditorWindow
{
    public AtlasVisualisationData visualizationData;

    private AtlasRectMapping selectedMapping;
    private List<AtlasRectMapping> relatedMappings;
    private AtlasRectMapping hoverMapping;

    private Texture2D originalTexture;
    private Texture2D heatmapTexture;

    private Vector2 leftPanelScroll;
    private Vector2 rightPanelScroll;

    private GUIStyle outlineStyle;

    // Heatmap mode variables
    private bool isHeatmapMode = false;
    private int heatmapDisplayMode = 0; // 0 = Max, 1 = Min, 2 = Mean
    private readonly string[] heatmapModeNames = { "Max", "Min", "Mean" };
    private Vector2 selectedPixelCoord = Vector2.zero;
    private bool hasSelectedPixel = false;

    private float globalMinDensity = 0f;
    private float globalMaxDensity = 0f;
    private bool hasCalculatedGlobalRange = false;

    private Dictionary<AtlasRectMapping, Rect> _cachedMappingUVRects;
    private Dictionary<AtlasRectMapping, List<MeshTriangleData>> _trianglesByMapping;
    private bool _mappingsBuilt = false;

    // Performance optimization: pixel query cache (no massive spatial grid needed)
    private Dictionary<Vector2Int, (List<MeshTriangleData> triangles, float minDensity, float maxDensity, float meanDensity)> _pixelQueryCache;
    private const int MAX_CACHE_SIZE = 100;

    [MenuItem("Art Tools/Atlas Visualiser")]
    public static void ShowWindow()
    {
        GetWindow<AtlasVisualiserWindow>("Atlas Visualiser");
    }

    private void OnEnable()
    {
        outlineStyle = new GUIStyle { normal = { background = EditorGUIUtility.whiteTexture } };

        // Reset spatial index state after domain reload to prevent null reference issues
        if (_trianglesByMapping == null || _cachedMappingUVRects == null)
        {
            _mappingsBuilt = false;
        }

        Selection.selectionChanged += CheckSelected;
    }
    
    private void OnDisable()
    {
        Selection.selectionChanged -= CheckSelected;
    }

    private void CheckSelected()
    {
        if (Selection.activeObject is AtlasVisualisationData data)
        {
            SetVisData(data);
        }
    }

    public void SetVisData(AtlasVisualisationData data)
    {
        if (visualizationData == data) return;
        visualizationData = data;
        selectedMapping = null;
        relatedMappings = new List<AtlasRectMapping>();
        originalTexture = null;
        if (heatmapTexture != null)
            DestroyImmediate(heatmapTexture);
        heatmapTexture = null;
        hasSelectedPixel = false;
        hasCalculatedGlobalRange = false;

        // Invalidate spatial index when data changes
        _mappingsBuilt = false;
        _cachedMappingUVRects = null;
        _trianglesByMapping = null;

        // Invalidate pixel query cache when data changes
        _pixelQueryCache = null;

        Repaint();
    }

    private void DrawPixelIndicator(Rect displayRect, Vector2 pixelCoord, Color color)
    {
        if (!hasSelectedPixel) return;

        // Convert pixel coordinates to display coordinates
        float xRatio = pixelCoord.x / visualizationData.atlasTexture.width;
        float yRatio = pixelCoord.y / visualizationData.atlasTexture.height;
        
        Vector2 displayPos = new Vector2(
            displayRect.x + xRatio * displayRect.width,
            displayRect.y + (1 - yRatio) * displayRect.height  // Flip Y coordinate
        );

        float size = 2.5f;
        float thickness = 1f;
        
        // Horizontal line
        EditorGUI.DrawRect(new Rect(
            displayPos.x - size, 
            displayPos.y - thickness/2, 
            size * 2, 
            thickness
        ), color);
        
        // Vertical line
        EditorGUI.DrawRect(new Rect(
            displayPos.x - thickness/2, 
            displayPos.y - size, 
            thickness, 
            size * 2
        ), color);
    }

    void OnGUI()
    {
        EditorGUILayout.LabelField("Atlas Data", EditorStyles.boldLabel);
        var data = (AtlasVisualisationData)EditorGUILayout.ObjectField(visualizationData, typeof(AtlasVisualisationData), false);
        SetVisData(data);

        EditorGUILayout.Space();

        // Mode selection
        EditorGUILayout.BeginHorizontal();
        var newIsHeatmapMode = EditorGUILayout.ToggleLeft("Heatmap", isHeatmapMode, GUILayout.Width(100));
        if (newIsHeatmapMode != isHeatmapMode)
        {
            isHeatmapMode = newIsHeatmapMode;
            hasSelectedPixel = false;
        }

        if (isHeatmapMode)
        {
            EditorGUILayout.LabelField("Metric:", GUILayout.Width(50));
            var newHeatmapDisplayMode = EditorGUILayout.Popup(heatmapDisplayMode, heatmapModeNames, GUILayout.Width(80));
            if (newHeatmapDisplayMode != heatmapDisplayMode)
            {
                heatmapDisplayMode = newHeatmapDisplayMode;
                if (heatmapTexture != null)
                    DestroyImmediate(heatmapTexture);
                heatmapTexture = null;
            }
        }
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space();

        if (visualizationData == null)
        {
            EditorGUILayout.HelpBox("Please assign an AtlasVisualizationData asset to begin.", MessageType.Info);
            return;
        }

        EditorGUILayout.BeginHorizontal(GUILayout.ExpandHeight(true));

        var keyWidth = 60f;
        var panelWidth = (position.width - keyWidth) * 0.5f - 5f;
        
        if (visualizationData?.atlasTexture != null)
        {
            DrawColorKeyColumn(keyWidth, panelWidth);
        }

        DrawLeftPanel(panelWidth);
        DrawRightPanel(panelWidth);

        EditorGUILayout.EndHorizontal();
    }

    private void DrawLeftPanel(float width)
    {
        EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.Width(width));
        leftPanelScroll = EditorGUILayout.BeginScrollView(leftPanelScroll);

        if (visualizationData.atlasTexture != null)
        {
            float aspect = (float)visualizationData.atlasTexture.width / visualizationData.atlasTexture.height;

            // Calculate the correct height to fit the texture within the available width
            float displayHeight = width / aspect;

            // Reserve exactly the space we need - no more, no less
            Rect displayRect = GUILayoutUtility.GetRect(width, displayHeight);

            if (isHeatmapMode)
            {
                if (heatmapTexture == null)
                    GenerateHeatmapTexture();

                if (heatmapTexture != null)
                    GUI.DrawTexture(displayRect, heatmapTexture, ScaleMode.ScaleToFit);
                
                if (hasSelectedPixel)
                    DrawPixelIndicator(displayRect, selectedPixelCoord, Color.yellow);
            }
            else
            {
                GUI.DrawTexture(displayRect, visualizationData.atlasTexture, ScaleMode.ScaleToFit);
            }

            if (selectedMapping != null)
            {
                foreach (var related in relatedMappings)
                {
                    var relRect = GetScaledRect(related.atlasPixelRect,
                        new Rect(0, 0,
                            visualizationData.atlasTexture.width,
                            visualizationData.atlasTexture.height),
                        displayRect);
                    DrawOutline(relRect, Color.grey, 1, isHeatmapMode ? 0f : 0.25f);
                }
                var selRect = GetScaledRect(selectedMapping.atlasPixelRect,
                    new Rect(0, 0,
                        visualizationData.atlasTexture.width,
                        visualizationData.atlasTexture.height),
                    displayRect);
                DrawOutline(selRect, Color.red, 2, isHeatmapMode ? 0f : 0.25f);
            }
#if HOVER_RECT
            if (hoverMapping != null && hoverMapping != selectedMapping)
            {
                var hovRect = GetScaledRect(hoverMapping.atlasPixelRect,
                    new Rect(0, 0,
                        visualizationData.atlasTexture.width,
                        visualizationData.atlasTexture.height),
                    displayRect);
                DrawOutline(hovRect, new Color(1f, 1f, 0f, 0.5f), 2, 0f);
            }
#endif

            var e = Event.current;
            if (displayRect.Contains(e.mousePosition))
            {
                if (e.type == EventType.MouseDown)
                {
                    if (isHeatmapMode)
                    {
                        HandleHeatmapClick(e.mousePosition, displayRect);
                        HandleMouseOver(e.mousePosition, true, displayRect); // Also handle selection in heatmap mode
                    }
                    else
                    {
                        HandleMouseOver(e.mousePosition, true, displayRect);
                    }
                    e.Use();
                }
                else
                {
                    if (!isHeatmapMode)
                        HandleMouseOver(e.mousePosition, false, displayRect);
                }
            }
        }
        else
        {
            EditorGUILayout.HelpBox("Please assign an AtlasVisualizationData asset to begin.", MessageType.Info);
        }

        EditorGUILayout.EndScrollView();
        EditorGUILayout.EndVertical();
    }

    private void DrawRightPanel(float width)
    {
        EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.Width(width));
        rightPanelScroll = EditorGUILayout.BeginScrollView(rightPanelScroll);

        if (isHeatmapMode)
        {
            DrawHeatmapModeRightPanel(width);
        }
        else
        {
            DrawNormalModeRightPanel(width);
        }

        EditorGUILayout.EndScrollView();
        EditorGUILayout.EndVertical();
    }

    private void HandleMouseOver(Vector2 mousePosition, bool isClick, Rect displayRect)
    {
        hoverMapping = null;

        foreach (var m in visualizationData.mappings)
        {
            Rect scaled = GetScaledRect(
                m.atlasPixelRect,
                new Rect(0, 0, visualizationData.atlasTexture.width, visualizationData.atlasTexture.height),
                displayRect
            );

            if (scaled.Contains(mousePosition))
            {
                if (isClick)
                {
                    selectedMapping = m;
                    relatedMappings = new List<AtlasRectMapping>();
                    foreach (var map in visualizationData.mappings)
                    {
                        if (map != m && map.originalTexturePath == m.originalTexturePath)
                            relatedMappings.Add(map);
                    }
                    originalTexture = LoadOriginalTexture(m.originalTexturePath);
                }
                else
                {
                    hoverMapping = m;
                }

                Repaint();
                return;
            }
        }

        if (!isClick)
        {
            hoverMapping = null;
            Repaint();
        }
    }
    
    private Texture2D LoadOriginalTexture(string path)
    {
        return string.IsNullOrEmpty(path) ? Texture2D.whiteTexture : AssetDatabase.LoadAssetAtPath<Texture2D>(path);
    }

    private Rect GetScaledRect(Rect sourceRect, Rect sourceTexRect, Rect displayRect)
    {
        float sx = displayRect.width  / sourceTexRect.width;
        float sy = displayRect.height / sourceTexRect.height;

        float flippedY = sourceTexRect.height - sourceRect.y - sourceRect.height;
        return new Rect(displayRect.x + sourceRect.x * sx, displayRect.y + flippedY * sy,
            sourceRect.width * sx, sourceRect.height * sy);
    }

    private void DrawOutline(Rect r, Color col, int border = 1, float fillAlpha = 0.25f)
    {
        var xMin = Mathf.FloorToInt(r.xMin);
        var xMax = Mathf.CeilToInt(r.xMax);
        var yMin = Mathf.FloorToInt(r.yMin);
        var yMax = Mathf.CeilToInt(r.yMax);
        var newXMin = xMin - border;
        var newXMax = xMax + border;
        var newYMin = yMin - border;
        var newYMax = yMax + border;

        EditorGUI.DrawRect(new Rect(newXMin, yMax, xMax - newXMin, newYMax - yMax), col);
        EditorGUI.DrawRect(new Rect(xMin, newYMin, newXMax - xMin, yMin - newYMin), col);
        EditorGUI.DrawRect(new Rect(newXMin, newYMin, xMin - newXMin, yMax - newYMin), col);
        EditorGUI.DrawRect(new Rect(xMax, yMin, newXMax - xMax, newYMax - yMin), col);
        if (fillAlpha > 0f)
        {
            Color fillColor = col;
            fillColor.a = fillAlpha;
            EditorGUI.DrawRect(new Rect(xMin, yMin, xMax - xMin, yMax - yMin), fillColor);
        }
    }

    private void GenerateHeatmapTexture()
    {
        if (visualizationData?.atlasTexture == null || visualizationData.triangleData == null)
            return;

        int width = visualizationData.atlasTexture.width;
        int height = visualizationData.atlasTexture.height;

        heatmapTexture = new Texture2D(width, height, TextureFormat.RGBAFloat, false);

        if (!hasCalculatedGlobalRange)
        {
            CalculateGlobalDensityRange();
        }

        var minDensityArray = new NativeArray<float>(width * height, Allocator.TempJob);
        var maxDensityArray = new NativeArray<float>(width * height, Allocator.TempJob);
        var meanDensityArray = new NativeArray<float>(width * height, Allocator.TempJob);
        var triangleCountArray = new NativeArray<int>(width * height, Allocator.TempJob);
        var pixelData = new NativeArray<Color>(width * height, Allocator.Persistent);

        try
        {
            for (int i = 0; i < minDensityArray.Length; i++)
            {
                minDensityArray[i] = float.MaxValue;
            }

            if (visualizationData.atlasTexture.isReadable)
            {
                var atlasColors = visualizationData.atlasTexture.GetPixels();
                NativeArray<Color>.Copy(atlasColors, pixelData);
            }

            foreach (var triangle in visualizationData.triangleData)
            {
                if (triangle.texelsPerMeter <= 0) 
                    continue;

                UpdatePixelStatsForTri(triangle, width, height, minDensityArray, maxDensityArray, meanDensityArray, triangleCountArray);
            }

            for (int i = 0; i < meanDensityArray.Length; i++)
            {
                if (triangleCountArray[i] > 0)
                {
                    meanDensityArray[i] /= triangleCountArray[i];
                }
            }

            var job = new HeatmapGenerationJob(maxDensityArray, minDensityArray, meanDensityArray, triangleCountArray,
                    globalMaxDensity, globalMinDensity, heatmapDisplayMode, pixelData);

            var handle = job.Schedule(width * height, 64);
            handle.Complete();

            heatmapTexture.SetPixelData(pixelData, 0);
            heatmapTexture.Apply();
        }
        finally
        {
            minDensityArray.Dispose();
            maxDensityArray.Dispose();
            meanDensityArray.Dispose();
            triangleCountArray.Dispose();
            //pixelData.Dispose();
        }
    }

    private void CalculateGlobalDensityRange()
    {
        if (visualizationData?.triangleData == null)
        {
            globalMinDensity = 0f;
            globalMaxDensity = 0f;
            hasCalculatedGlobalRange = true;
            return;
        }

        globalMinDensity = float.MaxValue;
        globalMaxDensity = 0f;

        foreach (var triangle in visualizationData.triangleData)
        {
            if (triangle.texelsPerMeter > 0)
            {
                globalMinDensity = Mathf.Min(globalMinDensity, triangle.texelsPerMeter);
                globalMaxDensity = Mathf.Max(globalMaxDensity, triangle.texelsPerMeter);
            }
        }

        if (globalMinDensity > 1e23f) globalMinDensity = 0f;
        hasCalculatedGlobalRange = true;
    }

    private void DrawColorKey(Rect keyRect)
    {
        if (!hasCalculatedGlobalRange) return;

        // Draw background
        EditorGUI.DrawRect(keyRect, new Color(0.2f, 0.2f, 0.2f, 0.8f));

        // Draw color gradient
        int gradientSteps = Mathf.FloorToInt(keyRect.height);
        for (int i = 0; i < gradientSteps; i++)
        {
            float t = 1f - (float)i / gradientSteps; // Top = 1 (max), Bottom = 0 (min)
            Color color = GetHeatmapColor(t);

            Rect colorRect = new Rect(keyRect.x + 40f, keyRect.y + i, keyRect.width - 40f, 1f);
            EditorGUI.DrawRect(colorRect, color);
        }

        // Draw border around gradient
        Rect borderRect = new Rect(keyRect.x + 40f, keyRect.y, keyRect.width - 40f, keyRect.height);
        EditorGUI.DrawRect(new Rect(borderRect.x - 1, borderRect.y - 1, borderRect.width + 2, 1), Color.gray);
        EditorGUI.DrawRect(new Rect(borderRect.x - 1, borderRect.yMax, borderRect.width + 2, 1), Color.gray);
        EditorGUI.DrawRect(new Rect(borderRect.x - 1, borderRect.y, 1, borderRect.height), Color.gray);
        EditorGUI.DrawRect(new Rect(borderRect.xMax, borderRect.y, 1, borderRect.height), Color.gray);

        // Draw labels
        GUIStyle labelStyle = new GUIStyle(EditorStyles.miniLabel);
        labelStyle.alignment = TextAnchor.MiddleRight;
        labelStyle.normal.textColor = Color.white;

        // Max label (top)
        Rect maxLabelRect = new Rect(keyRect.x, keyRect.y, 38f, 12f);
        GUI.Label(maxLabelRect, $"{globalMaxDensity:F0}", labelStyle);

        // Min label (bottom)
        Rect minLabelRect = new Rect(keyRect.x, keyRect.yMax - 12f, 38f, 12f);
        GUI.Label(minLabelRect, $"{globalMinDensity:F0}", labelStyle);

        // Unit label (middle)
        Rect unitLabelRect = new Rect(keyRect.x - 5f, keyRect.y + keyRect.height * 0.5f - 6f, 38f, 12f);
        labelStyle.fontSize = 8;
        GUI.Label(unitLabelRect, "tex/m", labelStyle);
    }

    private void UpdatePixelStatsForTri(MeshTriangleData triangle, int width, int height,
    NativeArray<float> pixelMinDensity, NativeArray<float> pixelMaxDensity, 
    NativeArray<float> pixelMeanDensity, NativeArray<int> pixelTriangleCount)
    {
        Vector2 p0 = new Vector2(triangle.uv0.x * width, triangle.uv0.y * height);
        Vector2 p1 = new Vector2(triangle.uv1.x * width, triangle.uv1.y * height);
        Vector2 p2 = new Vector2(triangle.uv2.x * width, triangle.uv2.y * height);

        int minX = Mathf.Max(0, Mathf.FloorToInt(Mathf.Min(p0.x, Mathf.Min(p1.x, p2.x))));
        int maxX = Mathf.Min(width - 1, Mathf.CeilToInt(Mathf.Max(p0.x, Mathf.Max(p1.x, p2.x))));
        int minY = Mathf.Max(0, Mathf.FloorToInt(Mathf.Min(p0.y, Mathf.Min(p1.y, p2.y))));
        int maxY = Mathf.Min(height - 1, Mathf.CeilToInt(Mathf.Max(p0.y, Mathf.Max(p1.y, p2.y))));

        for (int y = minY; y <= maxY; y++)
        {
            for (int x = minX; x <= maxX; x++)
            {
                Vector2 pixelCenter = new Vector2(x + 0.5f, y + 0.5f);

                if (IsPointInTriangle(pixelCenter, p0, p1, p2))
                {
                    int index = y * width + x;

                    pixelMinDensity[index] = math.min(pixelMinDensity[index], triangle.texelsPerMeter);
                    pixelMaxDensity[index] = math.max(pixelMaxDensity[index], triangle.texelsPerMeter);
                    pixelMeanDensity[index] += triangle.texelsPerMeter;
                    pixelTriangleCount[index]++;
                }
            }
        }
    }

    private Color GetHeatmapColor(float value)
    {
        // Blue to Red heatmap
        value = Mathf.Clamp01(value);

        return value switch
        {
            < 0.25f => Color.Lerp(Color.blue, Color.cyan, value * 4f),
            < 0.5f => Color.Lerp(Color.cyan, Color.green, (value - 0.25f) * 4f),
            < 0.75f => Color.Lerp(Color.green, Color.yellow, (value - 0.5f) * 4f),
            _ => Color.Lerp(Color.yellow, Color.red, (value - 0.75f) * 4f)
        };
    }

    private void HandleHeatmapClick(Vector2 mousePosition, Rect displayRect)
    {
        if (visualizationData?.atlasTexture == null)
            return;

        // Convert mouse position to atlas pixel coordinates
        Vector2 relativePos = (mousePosition - displayRect.position) / displayRect.size;
        selectedPixelCoord = new Vector2(
            relativePos.x * visualizationData.atlasTexture.width,
            (1f - relativePos.y) * visualizationData.atlasTexture.height // Flip Y
        );

        hasSelectedPixel = true;
        Repaint();
    }

    private void DrawHeatmapInfo()
    {
        EditorGUILayout.LabelField("Heatmap Information", EditorStyles.centeredGreyMiniLabel);

        if (!hasSelectedPixel)
        {
            EditorGUILayout.HelpBox("Click on the heatmap to see texel density information for that pixel.", MessageType.Info);
            return;
        }

        if (visualizationData?.triangleData == null)
        {
            EditorGUILayout.HelpBox("No triangle data available.", MessageType.Warning);
            return;
        }

        int x = Mathf.FloorToInt(selectedPixelCoord.x);
        int y = Mathf.FloorToInt(selectedPixelCoord.y);
        int width = visualizationData.atlasTexture.width;
        int height = visualizationData.atlasTexture.height;

        if (x < 0 || x >= width || y < 0 || y >= height)
            return;

        // Use optimized pixel query with spatial grid and caching
        var (coveringTriangles, minDensity, maxDensity, meanDensity) = GetTrianglesAtPixel(x, y);

        EditorGUILayout.LabelField($"Pixel: ({x}, {y})", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        if (coveringTriangles.Count == 0)
        {
            EditorGUILayout.LabelField("No triangles cover this pixel.");
            return;
        }

        EditorGUILayout.LabelField("Texel Density (texels/meter):");
        EditorGUILayout.LabelField($"  Max: {maxDensity:F2}");
        EditorGUILayout.LabelField($"  Min: {minDensity:F2}");
        EditorGUILayout.LabelField($"  Mean: {meanDensity:F2}");

        EditorGUILayout.Space();

        // Group triangles by mesh (with performance limit)
        var meshGroups = coveringTriangles.GroupBy(t => t.meshGuid).ToList();
        const int MAX_DISPLAYED_MESHES = 20; // Limit UI elements for performance

        EditorGUILayout.LabelField($"Meshes using this pixel ({meshGroups.Count}):");

        if (meshGroups.Count > MAX_DISPLAYED_MESHES)
        {
            EditorGUILayout.HelpBox($"Showing first {MAX_DISPLAYED_MESHES} of {meshGroups.Count} meshes for performance.", MessageType.Info);
        }

        int displayedCount = 0;
        foreach (var group in meshGroups)
        {
            if (displayedCount >= MAX_DISPLAYED_MESHES) break;

            var firstTriangle = group.First();
            var triangleCount = group.Count();
            var avgDensity = group.Average(t => t.texelsPerMeter);

            if (GUILayout.Button($"{firstTriangle.meshName} ({triangleCount} triangles): {avgDensity:F2} texels/m"))
            {
                var path = AssetDatabase.GUIDToAssetPath(firstTriangle.meshGuid);
                var obj = AssetDatabase.LoadAssetAtPath<Mesh>(path);
                if (obj != null)
                    EditorGUIUtility.PingObject(obj);
            }

            displayedCount++;
        }

        EditorGUILayout.Space();
    }

    private bool IsPointInTriangle(Vector2 point, Vector2 v0, Vector2 v1, Vector2 v2)
    {
        var denom = (v1.y - v2.y) * (v0.x - v2.x) + (v2.x - v1.x) * (v0.y - v2.y);
        if (Mathf.Abs(denom) < 0.0001f) return false;

        var a = ((v1.y - v2.y) * (point.x - v2.x) + (v2.x - v1.x) * (point.y - v2.y)) / denom;
        var b = ((v2.y - v0.y) * (point.x - v2.x) + (v0.x - v2.x) * (point.y - v2.y)) / denom;
        var c = 1 - a - b;

        return a >= -0.001f && b >= -0.001f && c >= -0.001f; // Small epsilon for edge cases
    }

    private void DrawColorKeyColumn(float width, float panelWidth)
    {
        EditorGUILayout.BeginVertical(GUILayout.Width(width));

        GUILayout.Space(5);

        Rect keyRect = GUILayoutUtility.GetRect(width, 300f);

        if (isHeatmapMode)
        {
            float aspect = (float)visualizationData.atlasTexture.width / visualizationData.atlasTexture.height;
            float atlasHeight = panelWidth / aspect;

            atlasHeight = Mathf.Clamp(atlasHeight, 100f, position.height - 150f);
            keyRect.height = atlasHeight;

            DrawColorKey(new Rect(keyRect.x + 5f, keyRect.y, width - 10f, keyRect.height));
        }

        EditorGUILayout.EndVertical();
    }

    private void DrawNormalModeRightPanel(float width)
    {
        EditorGUILayout.LabelField("Original Texture", EditorStyles.centeredGreyMiniLabel);

        if (selectedMapping == null)
        {
            EditorGUILayout.HelpBox("Click a region on the atlas to see the original texture here.", MessageType.Info);
        }
        else if (originalTexture == null)
        {
             EditorGUILayout.HelpBox($"Could not load texture from path:\n{selectedMapping.originalTexturePath}", MessageType.Warning);
        }
        else
        {
            EditorGUILayout.LabelField(selectedMapping.originalTexturePath, EditorStyles.wordWrappedMiniLabel);

            float aspectRatio = (float)originalTexture.width / originalTexture.height;
            Rect originalDisplayRect = GUILayoutUtility.GetRect(width, width / aspectRatio);

            GUI.DrawTexture(originalDisplayRect, originalTexture, ScaleMode.ScaleToFit);

            if (GUI.Button(originalDisplayRect, GUIContent.none, GUIStyle.none))
            {
                Selection.activeObject = originalTexture;
                EditorGUIUtility.PingObject(originalTexture);
            }

            Rect scaledOutlineRect = GetScaledRect(selectedMapping.originalPixelRect, new Rect(0, 0, originalTexture.width, originalTexture.height), originalDisplayRect);
            DrawOutline(scaledOutlineRect, Color.red, 2);
            foreach (var related in relatedMappings)
            {
                Rect relatedRect = GetScaledRect(related.originalPixelRect, new Rect(0, 0, originalTexture.width, originalTexture.height), originalDisplayRect);
                DrawOutline(relatedRect, Color.grey);
            }
        }
    }

    private void DrawHeatmapModeRightPanel(float width)
    {
        DrawHeatmapInfo();

        if (selectedMapping == null) return;
        
        EditorGUILayout.LabelField("Source Texture", EditorStyles.centeredGreyMiniLabel);
        EditorGUILayout.LabelField(selectedMapping.originalTexturePath, EditorStyles.wordWrappedMiniLabel);

        var sourceTexture = LoadOriginalTexture(selectedMapping.originalTexturePath);
        if (sourceTexture != null)
        {
            float aspectRatio = (float)sourceTexture.width / sourceTexture.height;
            Rect sourceDisplayRect = GUILayoutUtility.GetRect(width, width / aspectRatio);

            // Draw the original source texture
            GUI.DrawTexture(sourceDisplayRect, sourceTexture, ScaleMode.ScaleToFit);

            // Overlay heatmap triangles for this specific texture region
            DrawHeatmapTrianglesOverlayForTexture(sourceDisplayRect, sourceTexture);

            if (GUI.Button(sourceDisplayRect, GUIContent.none, GUIStyle.none))
            {
                Selection.activeObject = sourceTexture;
                EditorGUIUtility.PingObject(sourceTexture);
            }

            // Draw outline for the selected mapping region
            Rect scaledOutlineRect = GetScaledRect(selectedMapping.originalPixelRect, new Rect(0, 0, sourceTexture.width, sourceTexture.height), sourceDisplayRect);
            DrawOutline(scaledOutlineRect, Color.red, 2, 0f); // No fill for heatmap mode

            // Draw related mappings
            foreach (var related in relatedMappings)
            {
                Rect relatedRect = GetScaledRect(related.originalPixelRect, new Rect(0, 0, sourceTexture.width, sourceTexture.height), sourceDisplayRect);
                DrawOutline(relatedRect, Color.grey, 1, 0f); // No fill for heatmap mode
            }
        }

        EditorGUILayout.Space();

    }

    private void DrawHeatmapTrianglesOverlayForTexture(Rect displayRect, Texture2D sourceTexture)
    {
        if (visualizationData?.triangleData == null || !hasCalculatedGlobalRange)
            return;

        // Use try-finally to ensure GL state is properly restored
        GL.PushMatrix();
        try
        {
            GL.LoadPixelMatrix();
            GL.Begin(GL.TRIANGLES);

            // Build spatial index if not already built
            if (!_mappingsBuilt || _trianglesByMapping == null)
                BuildTriMappings();

            var mappingsToRender = new List<AtlasRectMapping>(relatedMappings);
            if (selectedMapping != null)
                mappingsToRender.Add(selectedMapping);

            foreach (var mapping in mappingsToRender)
            {
                if (_trianglesByMapping == null || !_trianglesByMapping.TryGetValue(mapping, out var trianglesInMapping))
                    continue;

                foreach (var triangle in trianglesInMapping)
                {
                    if (triangle.texelsPerMeter <= 0)
                        continue;

                    float normalizedDensity = (triangle.texelsPerMeter - globalMinDensity) / (globalMaxDensity - globalMinDensity + float.Epsilon);

                    Color heatmapColor = GetHeatmapColor(normalizedDensity);
                    heatmapColor.a = 0.5f;
                    GL.Color(heatmapColor);

                    Vector2 p0 = AtlasUVToSourceTextureCoord(triangle.uv0, mapping, sourceTexture, displayRect);
                    Vector2 p1 = AtlasUVToSourceTextureCoord(triangle.uv1, mapping, sourceTexture, displayRect);
                    Vector2 p2 = AtlasUVToSourceTextureCoord(triangle.uv2, mapping, sourceTexture, displayRect);

                    if (IsPointInRect(p0, displayRect) || IsPointInRect(p1, displayRect) || IsPointInRect(p2, displayRect))
                    {
                        GL.Vertex3(p0.x, p0.y, 0);
                        GL.Vertex3(p1.x, p1.y, 0);
                        GL.Vertex3(p2.x, p2.y, 0);
                    }
                }
            }

            GL.End();
        }
        finally
        {
            GL.PopMatrix();
        }
    }

    private bool DoesTriangleOverlapMapping(MeshTriangleData triangle, out AtlasRectMapping container)
    {
        // Build spatial index if not already built
        if (!_mappingsBuilt || _trianglesByMapping == null)
            BuildTriMappings();

        // Check only the selected mapping and related mappings using pre-built spatial index
        var mappingsToCheck = new List<AtlasRectMapping>(relatedMappings);
        if (selectedMapping != null)
            mappingsToCheck.Add(selectedMapping);

        foreach (var mapping in mappingsToCheck)
        {
            // Use spatial index to quickly check if triangle belongs to this mapping
            if (_trianglesByMapping != null &&
                _trianglesByMapping.TryGetValue(mapping, out var trianglesInMapping) &&
                trianglesInMapping.Contains(triangle))
            {
                container = mapping;
                return true;
            }
        }

        container = null;
        return false;
    }

    private Vector2 AtlasUVToSourceTextureCoord(Vector2 atlasUV, AtlasRectMapping mapping, Texture2D sourceTexture, 
        Rect displayRect)
    {
        float atlasX = atlasUV.x * visualizationData.atlasTexture.width;
        float atlasY = atlasUV.y * visualizationData.atlasTexture.height;

        float relativeX = (atlasX - mapping.atlasPixelRect.x) / mapping.atlasPixelRect.width;
        float relativeY = (atlasY - mapping.atlasPixelRect.y) / mapping.atlasPixelRect.height;

        if ((int)mapping.atlasPixelRect.x != (int)mapping.originalPixelRect.x)
            (relativeX, relativeY) = (relativeY, relativeX);
        
        float sourceX = mapping.originalPixelRect.x + relativeX * mapping.originalPixelRect.width;
        float sourceY = mapping.originalPixelRect.y + relativeY * mapping.originalPixelRect.height;

        float displayX = displayRect.x + (sourceX / sourceTexture.width) * displayRect.width;
        float displayY = displayRect.y + (1f - (sourceY / sourceTexture.height)) * displayRect.height;

        return new Vector2(displayX, displayY);
    }

    private bool IsPointInRect(Vector2 point, Rect rect)
    {
        return point.x >= rect.xMin && point.x <= rect.xMax && point.y >= rect.yMin && point.y <= rect.yMax;
    }

    private void BuildTriMappings()
    {
        if (_mappingsBuilt || visualizationData?.triangleData == null || visualizationData?.mappings == null)
            return;

        _cachedMappingUVRects = new Dictionary<AtlasRectMapping, Rect>();
        _trianglesByMapping = new Dictionary<AtlasRectMapping, List<MeshTriangleData>>();

        float atlasWidth = visualizationData.atlasTexture.width;
        float atlasHeight = visualizationData.atlasTexture.height;

        // Pre-calculate UV rects for all mappings
        foreach (var mapping in visualizationData.mappings)
        {
            Rect uvRect = new Rect(
                mapping.atlasPixelRect.x / atlasWidth,
                mapping.atlasPixelRect.y / atlasHeight,
                mapping.atlasPixelRect.width / atlasWidth,
                mapping.atlasPixelRect.height / atlasHeight
            );
            _cachedMappingUVRects[mapping] = uvRect;
            _trianglesByMapping[mapping] = new List<MeshTriangleData>();
        }

        // Build spatial index: assign triangles to mappings
        foreach (var triangle in visualizationData.triangleData)
        {
            if (triangle.texelsPerMeter <= 0) continue;

            foreach (var mapping in visualizationData.mappings)
            {
                var uvRect = _cachedMappingUVRects[mapping];

                // More comprehensive overlap test: check if triangle intersects rect
                if (DoesTriangleIntersectRect(triangle, uvRect))
                {
                    _trianglesByMapping[mapping].Add(triangle);
                }
            }
        }

        _mappingsBuilt = true;
    }

    private bool DoesTriangleIntersectRect(MeshTriangleData triangle, Rect rect)
    {
        // Check if any triangle vertex is inside the rect
        if (rect.Contains(triangle.uv0) || rect.Contains(triangle.uv1) || rect.Contains(triangle.uv2))
            return true;

        // Check if any rect corner is inside the triangle
        Vector2[] rectCorners = {
            new Vector2(rect.xMin, rect.yMin),
            new Vector2(rect.xMax, rect.yMin),
            new Vector2(rect.xMax, rect.yMax),
            new Vector2(rect.xMin, rect.yMax)
        };

        foreach (var corner in rectCorners)
        {
            if (IsPointInTriangle(corner, triangle.uv0, triangle.uv1, triangle.uv2))
                return true;
        }

        // Check if triangle edges intersect rect edges (more complex, but comprehensive)
        // For performance, we'll skip this for now as the above covers most cases

        return false;
    }

    private List<AtlasRectMapping> GetMappingsContainingPixel(int x, int y)
    {
        if (visualizationData?.mappings == null || visualizationData?.atlasTexture == null)
            return new List<AtlasRectMapping>();

        var result = new List<AtlasRectMapping>();

        foreach (var mapping in visualizationData.mappings)
        {
            // Check if pixel is within this mapping's atlas rect
            if (x >= mapping.atlasPixelRect.x && x < mapping.atlasPixelRect.x + mapping.atlasPixelRect.width &&
                y >= mapping.atlasPixelRect.y && y < mapping.atlasPixelRect.y + mapping.atlasPixelRect.height)
            {
                result.Add(mapping);
            }
        }

        return result;
    }

    private (List<MeshTriangleData> triangles, float minDensity, float maxDensity, float meanDensity) GetTrianglesAtPixel(int x, int y)
    {
        var pixelKey = new Vector2Int(x, y);

        // Check cache first
        if (_pixelQueryCache != null && _pixelQueryCache.TryGetValue(pixelKey, out var cachedResult))
        {
            return cachedResult;
        }

        // Initialize cache if needed
        if (_pixelQueryCache == null)
            _pixelQueryCache = new Dictionary<Vector2Int, (List<MeshTriangleData>, float, float, float)>();

        // Find which mappings contain this pixel
        var containingMappings = GetMappingsContainingPixel(x, y);

        // Build spatial index if not already built
        if (!_mappingsBuilt || _trianglesByMapping == null)
            BuildTriMappings();

        // Get triangles from only the relevant mappings
        var triangles = new List<MeshTriangleData>();
        Vector2 pixelUV = new Vector2((x + 0.5f) / visualizationData.atlasTexture.width,
                                     (y + 0.5f) / visualizationData.atlasTexture.height);

        foreach (var mapping in containingMappings)
        {
            if (_trianglesByMapping != null && _trianglesByMapping.TryGetValue(mapping, out var mappingTriangles))
            {
                // Only check triangles that belong to this mapping and actually contain the pixel
                foreach (var triangle in mappingTriangles)
                {
                    if (triangle.texelsPerMeter > 0 &&
                        IsPointInTriangle(pixelUV, triangle.uv0, triangle.uv1, triangle.uv2))
                    {
                        triangles.Add(triangle);
                    }
                }
            }
        }

        // Calculate density statistics
        float minDensity = float.MaxValue;
        float maxDensity = 0f;
        float totalDensity = 0f;
        int validTriangles = 0;

        foreach (var triangle in triangles)
        {
            if (triangle.texelsPerMeter > 0)
            {
                minDensity = Mathf.Min(minDensity, triangle.texelsPerMeter);
                maxDensity = Mathf.Max(maxDensity, triangle.texelsPerMeter);
                totalDensity += triangle.texelsPerMeter;
                validTriangles++;
            }
        }

        if (validTriangles == 0)
            minDensity = 0f;
        float meanDensity = validTriangles > 0 ? totalDensity / validTriangles : 0f;

        var result = (triangles, minDensity, maxDensity, meanDensity);

        // Cache the result (with size limit)
        if (_pixelQueryCache.Count >= MAX_CACHE_SIZE)
        {
            // Remove oldest entry (simple FIFO)
            var firstKey = _pixelQueryCache.Keys.First();
            _pixelQueryCache.Remove(firstKey);
        }
        _pixelQueryCache[pixelKey] = result;

        return result;
    }

}
