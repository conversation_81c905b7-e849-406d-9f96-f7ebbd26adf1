using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEditor.IMGUI.Controls;
using UnityEngine;

public class ORSizeTab : OptimiserReportTab
{
    private const int MaxFilesToShow = 100;

    private TreeViewState _treeState;
    private AssetTreeView _treeView;

    private FileNode _rootFileNode;
    private FileNode _selectedFile;

    private readonly Dictionary<string, Type> _typeCache = new();
    private OptimiserReport _cachedReport;

    private bool _sortByName;
    private bool _foldersFirst;
    private Vector2 _treeScroll;

    private void OnEnable()
    {
        m_title = "File Sizes";
    }

    public override void Draw(OptimiserReport report, OptimiserReportEditor editor)
    {
        if (report != _cachedReport)
        {
            BuildDataStructures(report);
            _cachedReport = report;
        }

        // Return if root is null, indicating no data was built
        if (_rootFileNode == null)
        {
            EditorGUILayout.HelpBox("No input folders found in the report to analyze.", MessageType.Info);
            return;
        }

        float halfWidth = Mathf.Floor(EditorGUIUtility.currentViewWidth * 0.5f) - 20;

        EditorGUILayout.BeginHorizontal();

        // Left Panel: Tree View
        GUILayout.BeginVertical(GUI.skin.box, GUILayout.Width(halfWidth), GUILayout.MaxHeight(600));
        _treeScroll = EditorGUILayout.BeginScrollView(_treeScroll, GUILayout.ExpandHeight(true));
        Rect treeRect = GUILayoutUtility.GetRect(0, _treeView.totalHeight, GUILayout.ExpandWidth(true));
        _treeView.OnGUI(treeRect);
        EditorGUILayout.EndScrollView();
        GUILayout.EndVertical();

        // Right Panel: Details
        GUILayout.BeginVertical(GUI.skin.box, GUILayout.Width(halfWidth), GUILayout.ExpandHeight(true));
        GUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();
        
        var btnLabel = $"Sort by: {(_sortByName ? "Name" : "Size")}";
        if (GUILayout.Button(btnLabel, GUILayout.Width(120)))
        {
            _sortByName = !_sortByName;
            _treeView.SaveExpanded();
            _treeView.SortByName = _sortByName;
            _treeView.Reload();
            _treeView.RestoreExpanded();
        }
        
        var folderBtnLabel = $"Folders: {(_foldersFirst ? "First" : "Mixed")}";
        if (GUILayout.Button(folderBtnLabel, GUILayout.Width(120)))
        {
            _foldersFirst = !_foldersFirst;
            _treeView.SaveExpanded();
            _treeView.FoldersFirst = _foldersFirst;
            _treeView.Reload();
            _treeView.RestoreExpanded();
        }
        
        GUILayout.FlexibleSpace();
        GUILayout.EndHorizontal();

        DrawDetailsPanel();
        GUILayout.EndVertical();

        EditorGUILayout.EndHorizontal();
    }

    private void DrawDetailsPanel()
    {
        if (_selectedFile == null)
        {
            EditorGUILayout.HelpBox("Select a folder or file in the tree.", MessageType.Info);
            return;
        }

        if (_selectedFile.IsDirectory)
        {
            EditorGUILayout.LabelField("Folder Details", EditorStyles.boldLabel);
            DrawDoubleLabel("Path:", _selectedFile.Path, EditorStyles.wordWrappedLabel);
            DrawDoubleLabel("Total Size:", FormatSize(_selectedFile.TotalSize), EditorStyles.wordWrappedLabel);

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Size by Asset Type:", EditorStyles.boldLabel);

            if (_selectedFile.TypeSizes == null) 
                return;
            
            foreach (var (key, val) in _selectedFile.TypeSizes.OrderByDescending(x => x.Value))
            {
                if (val <= 0)
                    break;
                DrawDoubleLabel(key.Name, FormatSize(val), EditorStyles.wordWrappedLabel);
            }
        }
        else // It's a file
        {
            EditorGUILayout.LabelField("File Details", EditorStyles.boldLabel);
            DrawDoubleLabel("Path:", _selectedFile.Path, EditorStyles.wordWrappedLabel);
            DrawDoubleLabel("Size:", FormatSize(_selectedFile.TotalSize), EditorStyles.wordWrappedLabel);

            var fileType = GetAssetType(_selectedFile.Path);
            DrawDoubleLabel("Type:", fileType.Name, EditorStyles.wordWrappedLabel);

            if (GUILayout.Button("Ping in Project"))
            {
                var asset = AssetDatabase.LoadMainAssetAtPath(_selectedFile.Path);
                if(asset != null) EditorGUIUtility.PingObject(asset);
            }

            // Example of file-specific UI
            if (fileType != typeof(Texture2D))
                return;

            var path = _selectedFile.Path;
            var texFolder = Path.GetDirectoryName(path);
            var visDataFile = path[texFolder.Length..path.LastIndexOf('_')] + "_VisData.asset";
            var visDataPath = $"{texFolder}/Editor{visDataFile}".Replace("\\", "/");

            var visData = AssetDatabase.LoadAssetAtPath<AtlasVisualisationData>(visDataPath);
            if (visData == null || !GUILayout.Button("Open Atlas Visualiser"))
                return;

            EditorGUIUtility.PingObject(visData);
            var window = EditorWindow.GetWindow<AtlasVisualiserWindow>("Atlas Visualiser");
            window.SetVisData(visData);
            window.Show();
        }
    }

    private void OnTreeSelectionChanged(IList<int> selectedIds)
    {
        _selectedFile = null;

        if (selectedIds.Count == 0) return;
        
        if (_treeView.GetItem(selectedIds[0], _treeView.GetRoot()) is AssetTreeView.FileTreeViewItem item)
            _selectedFile = item.Data;
    }

    private void BuildDataStructures(OptimiserReport report)
    {
        _typeCache.Clear();

        _rootFileNode = new FileNode("Assets");

        foreach (var folderInfo in report.folderInfos)
        {
            var toPath = folderInfo.folderTo;
            if (Directory.Exists(toPath))
                BuildTreeRecursively(toPath, _rootFileNode);
        }

        CalculateSizesAndPopulateCaches(_rootFileNode);

        _treeState ??= new TreeViewState();
        _treeView = new AssetTreeView(_treeState, _rootFileNode) { SortByName = _sortByName, FoldersFirst = _foldersFirst };
        _treeView.OnSelectionChanged += OnTreeSelectionChanged;
        _treeView.Reload();
    }

    private void BuildTreeRecursively(string path, FileNode parentNode)
    {
        string[] parts = path.Split(new[] { '/', '\\' }, StringSplitOptions.RemoveEmptyEntries);
        if (parts[^1] == "Editor")
            return; // Skip Editor folders
        
        FileNode currentNode = parentNode;
        string currentPath = "";

        foreach (var part in parts)
        {
            currentPath = string.IsNullOrEmpty(currentPath) ? part : $"{currentPath}/{part}";
            if (!currentNode.Children.TryGetValue(currentPath, out var nextNode))
            {
                nextNode = new FileNode(part, currentPath);
                currentNode.Children[currentPath] = nextNode;
            }

            currentNode = nextNode;
        }

        foreach (var filePath in Directory.GetFiles(path))
        {
            if (filePath.EndsWith(".meta")) 
                continue;
            
            var fileInfo = new FileInfo(filePath);
            var assetPath = filePath.Replace("\\", "/");
            if (!currentNode.Children.ContainsKey(assetPath))
            {
                var fileNode = new FileNode(fileInfo);
                currentNode.Children[assetPath] = fileNode;
            }
        }

        foreach (var dirPath in Directory.GetDirectories(path))
            BuildTreeRecursively(dirPath, parentNode);
    }

    private void CalculateSizesAndPopulateCaches(FileNode node)
    {
        if (!node.IsDirectory)
        {
            Type assetType = GetAssetType(node.Path);
            node.TypeSizes[assetType] = node.TotalSize;
            return;
        }

        long totalSize = 0;
        var typeSizes = new Dictionary<Type, long>();

        foreach (var childNode in node.Children.Values)
        {
            CalculateSizesAndPopulateCaches(childNode);

            totalSize += childNode.TotalSize;
            
            foreach (var (type, size) in childNode.TypeSizes)
            {
                if (!typeSizes.TryAdd(type, size))
                    typeSizes[type] += size;
            }
        }

        node.TotalSize = totalSize;
        node.TypeSizes = typeSizes;
    }

    private static string FormatSize(long b)
    {
        return b switch
        {
            >= 1L << 40 => $"{(double)b / (1L << 40):F2} TB",
            >= 1 << 30 => $"{(double)b / (1 << 30):F2} GB",
            >= 1 << 20 => $"{(double)b / (1 << 20):F2} MB",
            >= 1 << 10 => $"{(double)b / (1 << 10):F2} KB",
            _ => $"{b} B"
        };
    }

    private Type GetAssetType(string assetPath)
    {
        if (!_typeCache.TryGetValue(assetPath, out var type))
        {
            type = AssetDatabase.GetMainAssetTypeAtPath(assetPath) ?? typeof(UnityEngine.Object);
            _typeCache[assetPath] = type;
        }
        return type;
    }
    
    private class FileNode
    {
        public string Name { get; }
        public string Path { get; }
        public bool IsDirectory => !Name.Contains('.');
        public long TotalSize { get; set; }
        public Dictionary<Type, long> TypeSizes { get; set; } = new();
        public Dictionary<string, FileNode> Children { get; } = new();

        public FileNode(string name, string path)
        {
            Name = name;
            Path = path;
            TotalSize = 0;
        }
        
        public FileNode(string name) : this(name, name) { }
        
        public FileNode(FileInfo fileInfo)
        {
            Name = fileInfo.Name;
            Path = fileInfo.FullName.Replace("\\", "/").Replace(Application.dataPath, "Assets");
            TotalSize = fileInfo.Length;
        }
    }

    private class AssetTreeView : TreeView
    {
        public bool SortByName { get; set; }
        public bool FoldersFirst { get; set; }

        private List<string> _expandedPaths;
        private readonly FileNode _rootData;
        private int _nextId;
        public Action<IList<int>> OnSelectionChanged;

        public class FileTreeViewItem : TreeViewItem
        {
            public FileNode Data;
        }

        public AssetTreeView(TreeViewState state, FileNode root) : base(state)
        {
            _rootData = root;
            showAlternatingRowBackgrounds = true;
        }
        
        public TreeViewItem GetRoot() => rootItem;
        public TreeViewItem GetItem(int id, TreeViewItem root) => FindItem(id, root);

        public void SaveExpanded()
        {
            _expandedPaths = new List<string>();
            var expanded = GetExpanded();
            foreach (var id in expanded)
            {
                if (FindItem(id, rootItem) is FileTreeViewItem item && item.Data.IsDirectory)
                {
                    _expandedPaths.Add(item.Data.Path);
                }
            }
        }

        public void RestoreExpanded()
        {
            if (_expandedPaths == null || _expandedPaths.Count == 0) 
                return;

            var expandedIDs = new HashSet<int>();
            FindItemsByPath(rootItem, new HashSet<string>(_expandedPaths), expandedIDs);
            SetExpanded(expandedIDs.ToList());
        }

        void FindItemsByPath(TreeViewItem parent, ISet<string> pathsToFind, HashSet<int> expandedIDs)
        {
            if (parent.children == null) 
                return;
            
            foreach (TreeViewItem child in parent.children)
            {
                if (child is FileTreeViewItem fileItem && pathsToFind.Contains(fileItem.Data.Path))
                    expandedIDs.Add(fileItem.id);
                if (child.hasChildren)
                    FindItemsByPath(child, pathsToFind, expandedIDs);
            }
        }
        
        protected override TreeViewItem BuildRoot()
        {
            _nextId = 0;
            var root = new TreeViewItem { id = _nextId++, depth = -1, displayName = "Root" };
            
            if (_rootData != null)
            {
                AddChildNodesRecursive(root, _rootData, 0);
            }
            
            SetupDepthsFromParentsAndChildren(root);
            return root;
        }

        private void AddChildNodesRecursive(TreeViewItem parent, FileNode nodeData, int depth)
        {
            IOrderedEnumerable<FileNode> orderedChildren;
            if (FoldersFirst)
            {
                orderedChildren = SortByName
                    ? nodeData.Children.Values.OrderByDescending(c => c.IsDirectory).ThenBy(c => c.Name)
                    : nodeData.Children.Values.OrderByDescending(c => c.IsDirectory).ThenByDescending(c => c.TotalSize);
            }
            else
            {
                orderedChildren = SortByName
                    ? nodeData.Children.Values.OrderBy(c => c.Name)
                    : nodeData.Children.Values.OrderByDescending(c => c.TotalSize);
            }

            foreach (var childData in orderedChildren.Take(MaxFilesToShow))
            {
                var displayName = $"{childData.Name} ({FormatSize(childData.TotalSize)})";
                
                var item = new FileTreeViewItem
                {
                    id = _nextId++,
                    depth = depth,
                    displayName = displayName,
                    Data = childData
                };

                parent.AddChild(item);

                if (childData.IsDirectory && childData.Children.Count > 0)
                {
                    AddChildNodesRecursive(item, childData, depth + 1);
                }
            }
        }

        protected override void SelectionChanged(IList<int> selectedIds)
        {
            OnSelectionChanged?.Invoke(selectedIds);
            base.SelectionChanged(selectedIds);
        }
    }
}