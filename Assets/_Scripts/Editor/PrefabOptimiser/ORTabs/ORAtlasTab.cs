using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;

public class ORAtlasTab : OptimiserReportTab
{
    private enum SortMode
    {
        AtlasName,
        MinTexelsPerMeter,
        MaxTexelsPerMeter,
        MeanTexelsPerMeter,
        TriangleCount
    }

    private class AtlasInfo
    {
        public string atlasName;
        public string shaderName;
        public AtlasVisualisationData visData;
        public float minTexelsPerMeter;
        public float maxTexelsPerMeter;
        public float meanTexelsPerMeter;
        public int triangleCount;
        public Texture2D atlasTexture;
        public string assetPath;

        private Texture2D _cachedPreviewTexture;
        private bool _previewTextureLoaded = false;

        public Texture2D GetPreviewTexture()
        {
            if (!_previewTextureLoaded)
            {
                _previewTextureLoaded = true;
                _cachedPreviewTexture = atlasTexture;
            }
            return _cachedPreviewTexture;
        }
    }

    private List<AtlasInfo> _atlasInfos;
    private OptimiserReport _cachedReport;
    private Vector2 _scrollPosition;
    private SortMode _sortMode = SortMode.MeanTexelsPerMeter;
    private bool _sortAscending = false;

    // Performance caching for hundreds of atlases
    private float _cachedGlobalMin = float.MaxValue;
    private float _cachedGlobalMax = float.MinValue;
    private bool _globalRangeCached = false;
    private List<AtlasInfo> _sortedAtlasCache;
    private SortMode _lastSortMode;
    private bool _lastSortAscending;

    private void OnEnable()
    {
        m_title = "Atlases";
    }

    public override void Draw(OptimiserReport report, OptimiserReportEditor editor)
    {
        if (report != _cachedReport)
        {
            GatherAtlasData(report);
            _cachedReport = report;
            // Invalidate all caches when report changes
            InvalidateCaches();
        }

        if (_atlasInfos == null || _atlasInfos.Count == 0)
        {
            EditorGUILayout.HelpBox("No atlas visualization data found in this report.", MessageType.Info);
            return;
        }

        DrawSummary();
        DrawToolbar();
        DrawAtlasList();
    }

    private void InvalidateCaches()
    {
        _globalRangeCached = false;
        _sortedAtlasCache = null;
        _cachedGlobalMin = float.MaxValue;
        _cachedGlobalMax = float.MinValue;
    }

    private void DrawSummary()
    {
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        EditorGUILayout.LabelField("Atlas Summary", EditorStyles.boldLabel);

        if (_atlasInfos.Count > 0)
        {
            var totalTriangles = _atlasInfos.Sum(a => a.triangleCount);
            var (globalMin, globalMax) = GetCachedGlobalRange();
            var overallMean = _atlasInfos.SelectMany(a => Enumerable.Repeat(a.meanTexelsPerMeter, a.triangleCount)).Average();

            DrawDoubleLabel("Total Atlases:", _atlasInfos.Count.ToString(), EditorStyles.label);
            DrawDoubleLabel("Total Triangles:", totalTriangles.ToString("N0"), EditorStyles.label);
            DrawDoubleLabel("Global Min tex/m:", globalMin.ToString("F1"), EditorStyles.label);
            DrawDoubleLabel("Global Max tex/m:", globalMax.ToString("F1"), EditorStyles.label);
            DrawDoubleLabel("Overall Mean tex/m:", overallMean.ToString("F1"), EditorStyles.label);
        }
        else
        {
            EditorGUILayout.LabelField("No atlas data available", EditorStyles.centeredGreyMiniLabel);
        }

        EditorGUILayout.EndVertical();
        EditorGUILayout.Space();
    }

    private (float min, float max) GetCachedGlobalRange()
    {
        if (!_globalRangeCached && _atlasInfos?.Count > 0)
        {
            _cachedGlobalMin = _atlasInfos.Min(a => a.minTexelsPerMeter);
            _cachedGlobalMax = _atlasInfos.Max(a => a.maxTexelsPerMeter);
            _globalRangeCached = true;
        }
        return (_cachedGlobalMin, _cachedGlobalMax);
    }

    private void DrawToolbar()
    {
        EditorGUILayout.BeginHorizontal();

        EditorGUILayout.LabelField("Sort by:", GUILayout.Width(60));

        var sortModeNames = new[] { "Atlas Name", "Min texel density", "Max texel density", "Mean texel density", "Triangle Count" };
        var newSortMode = (SortMode)EditorGUILayout.Popup((int)_sortMode, sortModeNames, GUILayout.Width(120));

        if (newSortMode != _sortMode)
        {
            _sortMode = newSortMode;
            _sortedAtlasCache = null; // Invalidate sort cache
        }

        var orderLabel = _sortAscending ? "↑" : "↓";
        if (GUILayout.Button(orderLabel, GUILayout.Width(25)))
        {
            _sortAscending = !_sortAscending;
            _sortedAtlasCache = null; // Invalidate sort cache
        }

        GUILayout.FlexibleSpace();
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space();
    }

    private void DrawAtlasList()
    {
        _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);

        var sortedAtlases = GetSortedAtlases().ToList();

        // Header
        EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
        EditorGUILayout.LabelField("Preview", EditorStyles.toolbarButton, GUILayout.Width(80));
        EditorGUILayout.LabelField("Atlas Name", EditorStyles.toolbarButton, GUILayout.Width(150));
        EditorGUILayout.LabelField("Shader", EditorStyles.toolbarButton, GUILayout.Width(200));
        EditorGUILayout.LabelField("Min tex/m", EditorStyles.toolbarButton, GUILayout.Width(80));
        EditorGUILayout.LabelField("Max tex/m", EditorStyles.toolbarButton, GUILayout.Width(80));
        EditorGUILayout.LabelField("Mean tex/m", EditorStyles.toolbarButton, GUILayout.Width(80));
        EditorGUILayout.LabelField("Triangles", EditorStyles.toolbarButton, GUILayout.Width(80));
        EditorGUILayout.EndHorizontal();

        // Performance optimization: For very large lists (>100 atlases), consider virtualization
        // For now, we'll render all but could implement virtual scrolling if needed
        if (sortedAtlases.Count > 100)
        {
            EditorGUILayout.HelpBox($"Displaying {sortedAtlases.Count} atlases. Performance may be affected with very large lists.", MessageType.Info);
        }

        foreach (var atlasInfo in sortedAtlases)
        {
            DrawAtlasRow(atlasInfo);
        }

        EditorGUILayout.EndScrollView();
    }

    private void DrawAtlasRow(AtlasInfo atlasInfo)
    {
        EditorGUILayout.BeginHorizontal(EditorStyles.helpBox);

        // Preview thumbnail (using cached texture for performance)
        var previewRect = GUILayoutUtility.GetRect(75, 75, GUILayout.Width(80));
        var previewTexture = atlasInfo.GetPreviewTexture();
        if (previewTexture != null)
        {
            GUI.DrawTexture(previewRect, previewTexture, ScaleMode.ScaleToFit);
        }
        else
        {
            EditorGUI.DrawRect(previewRect, Color.gray);
            GUI.Label(previewRect, "No Preview", EditorStyles.centeredGreyMiniLabel);
        }

        EditorGUILayout.BeginVertical();

        // Atlas info
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField(atlasInfo.atlasName, EditorStyles.boldLabel, GUILayout.Width(150));

        var shaderStyle = new GUIStyle(EditorStyles.label);
        var shaderContent = new GUIContent(atlasInfo.shaderName);
        var shaderRect = GUILayoutUtility.GetRect(shaderContent, shaderStyle, GUILayout.Width(200));
        GUI.Label(shaderRect, shaderContent, shaderStyle);

        EditorGUILayout.LabelField($"{atlasInfo.minTexelsPerMeter:F1}", GUILayout.Width(80));
        EditorGUILayout.LabelField($"{atlasInfo.maxTexelsPerMeter:F1}", GUILayout.Width(80));
        EditorGUILayout.LabelField($"{atlasInfo.meanTexelsPerMeter:F1}", GUILayout.Width(80));
        EditorGUILayout.LabelField($"{atlasInfo.triangleCount:N0}", GUILayout.Width(80));

        EditorGUILayout.EndHorizontal();

        // Density visualization bar
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("Density Range:", EditorStyles.miniLabel, GUILayout.Width(90));
        DrawDensityBar(atlasInfo);
        
        GUILayout.Space(5);
        
        if (GUILayout.Button("Open Visualizer", GUILayout.Width(120)))
        {
            OpenAtlasVisualizer(atlasInfo);
        }

        if (GUILayout.Button("Select Asset", GUILayout.Width(100)))
        {
            Selection.activeObject = atlasInfo.visData;
            EditorGUIUtility.PingObject(atlasInfo.visData);
        }
        
        EditorGUILayout.EndHorizontal();

        // Additional info row
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField($"Path: {atlasInfo.assetPath}", EditorStyles.miniLabel);
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.EndVertical();
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space(5);
    }

    private void DrawDensityBar(AtlasInfo atlasInfo)
    {
        var barRect = GUILayoutUtility.GetRect(200, 16, GUILayout.Width(200), GUILayout.Height(16));
        barRect.y += 4;

        // Calculate consistent padding (2px on all sides)
        const float padding = 2f;
        var innerRect = new Rect(
            barRect.x + padding,
            barRect.y + padding,
            barRect.width - (padding * 2),
            barRect.height - (padding * 2)
        );

        // Background
        EditorGUI.DrawRect(barRect, new Color(0.3f, 0.3f, 0.3f));

        // Get cached global min/max for consistent scaling across all atlases
        var (globalMin, globalMax) = GetCachedGlobalRange();

        if (globalMax > globalMin)
        {
            // Calculate positions for min, mean, and max
            float minPos = (atlasInfo.minTexelsPerMeter - globalMin) / (globalMax - globalMin);
            float meanPos = (atlasInfo.meanTexelsPerMeter - globalMin) / (globalMax - globalMin);
            float maxPos = (atlasInfo.maxTexelsPerMeter - globalMin) / (globalMax - globalMin);

            // Draw gradient range bar (from min to max) with smooth color transition
            var rangeRect = new Rect(
                innerRect.x + minPos * innerRect.width,
                innerRect.y,
                (maxPos - minPos) * innerRect.width,
                innerRect.height
            );

            // Draw gradient with pixel-by-pixel color interpolation
            int gradientSteps = Mathf.Max(1, Mathf.FloorToInt(rangeRect.width));
            for (int i = 0; i < gradientSteps; i++)
            {
                float t = (float)i / gradientSteps;
                float globalT = minPos + t * (maxPos - minPos);
                Color gradientColor = GetHeatmapColor(globalT);
                gradientColor.a = 0.8f;

                Rect pixelRect = new Rect(rangeRect.x + i, rangeRect.y, 1f, rangeRect.height);
                EditorGUI.DrawRect(pixelRect, gradientColor);
            }

            // Draw mean marker with enhanced visibility
            var meanRect = new Rect(
                innerRect.x + meanPos * innerRect.width - 1,
                barRect.y,
                2,
                barRect.height
            );
            EditorGUI.DrawRect(meanRect, Color.white);

            // Add a subtle outline to the mean marker for better visibility
            EditorGUI.DrawRect(new Rect(meanRect.x - 1, meanRect.y, 1, meanRect.height), Color.black);
            EditorGUI.DrawRect(new Rect(meanRect.xMax, meanRect.y, 1, meanRect.height), Color.black);
        }

        // Border
        EditorGUI.DrawRect(new Rect(barRect.x, barRect.y, barRect.width, 1), Color.gray);
        EditorGUI.DrawRect(new Rect(barRect.x, barRect.yMax - 1, barRect.width, 1), Color.gray);
        EditorGUI.DrawRect(new Rect(barRect.x, barRect.y, 1, barRect.height), Color.gray);
        EditorGUI.DrawRect(new Rect(barRect.xMax - 1, barRect.y, 1, barRect.height), Color.gray);
    }

    private Color GetHeatmapColor(float value)
    {
        // Blue to Red heatmap (same as AtlasVisualiserWindow)
        value = Mathf.Clamp01(value);

        return value switch
        {
            < 0.25f => Color.Lerp(Color.blue, Color.cyan, value * 4f),
            < 0.5f => Color.Lerp(Color.cyan, Color.green, (value - 0.25f) * 4f),
            < 0.75f => Color.Lerp(Color.green, Color.yellow, (value - 0.5f) * 4f),
            _ => Color.Lerp(Color.yellow, Color.red, (value - 0.75f) * 4f)
        };
    }

    private void OpenAtlasVisualizer(AtlasInfo atlasInfo)
    {
        var window = EditorWindow.GetWindow<AtlasVisualiserWindow>("Atlas Visualiser");
        window.SetVisData(atlasInfo.visData);
        window.Show();
        window.Focus();
    }

    private IEnumerable<AtlasInfo> GetSortedAtlases()
    {
        // Use cached sorted list if sort parameters haven't changed
        if (_sortedAtlasCache != null && _lastSortMode == _sortMode && _lastSortAscending == _sortAscending)
        {
            return _sortedAtlasCache;
        }

        var sortedList = _atlasInfos.ToList();

        sortedList.Sort((a, b) =>
        {
            int comparison = _sortMode switch
            {
                SortMode.AtlasName => string.Compare(a.atlasName, b.atlasName, StringComparison.OrdinalIgnoreCase),
                SortMode.MinTexelsPerMeter => a.minTexelsPerMeter.CompareTo(b.minTexelsPerMeter),
                SortMode.MaxTexelsPerMeter => a.maxTexelsPerMeter.CompareTo(b.maxTexelsPerMeter),
                SortMode.MeanTexelsPerMeter => a.meanTexelsPerMeter.CompareTo(b.meanTexelsPerMeter),
                SortMode.TriangleCount => a.triangleCount.CompareTo(b.triangleCount),
                _ => 0
            };

            return _sortAscending ? comparison : -comparison;
        });

        // Cache the sorted result
        _sortedAtlasCache = sortedList;
        _lastSortMode = _sortMode;
        _lastSortAscending = _sortAscending;

        return sortedList;
    }

    private void GatherAtlasData(OptimiserReport report)
    {
        _atlasInfos = new List<AtlasInfo>();

        // Get relevant folder paths from the report
        var relevantFolders = new HashSet<string>();
        foreach (var folderInfo in report.folderInfos)
        {
            if (Directory.Exists(folderInfo.folderTo))
            {
                var atlasFolder = Path.Combine(folderInfo.folderTo, "Atlases");
                if (Directory.Exists(atlasFolder))
                {
                    relevantFolders.Add(atlasFolder);
                }
            }
        }

        // Find all AtlasVisualisationData assets in the project
        var visDataGuids = AssetDatabase.FindAssets("t:AtlasVisualisationData");

        foreach (var guid in visDataGuids)
        {
            var assetPath = AssetDatabase.GUIDToAssetPath(guid);
            var visData = AssetDatabase.LoadAssetAtPath<AtlasVisualisationData>(assetPath);

            // Check if this atlas is relevant to the current report
            bool isRelevant = relevantFolders.Count == 0 || // If no relevant folders found, show all
                             relevantFolders.Any(folder => assetPath.StartsWith(folder.Replace("\\", "/")));

            if (!isRelevant || visData?.triangleData == null || visData.triangleData.Count <= 0) continue;
            var atlasInfo = CreateAtlasInfo(visData, assetPath);
            if (atlasInfo != null)
            {
                _atlasInfos.Add(atlasInfo);
            }
        }

        // Sort by atlas name by default
        _atlasInfos.Sort((a, b) => string.Compare(a.atlasName, b.atlasName, StringComparison.OrdinalIgnoreCase));
    }

    private AtlasInfo CreateAtlasInfo(AtlasVisualisationData visData, string assetPath)
    {
        if (visData.triangleData == null || visData.triangleData.Count == 0)
            return null;

        // Extract atlas name and shader name from the asset path
        var fileName = Path.GetFileNameWithoutExtension(assetPath);
        var parts = fileName.Replace("_VisData", "").Split('_');
        
        string atlasName = "Unknown";
        string shaderName = "Unknown";
        
        if (parts.Length >= 2)
        {
            atlasName = parts[0];
            shaderName = string.Join("_", parts.Skip(1));
        }
        else if (parts.Length == 1)
        {
            atlasName = parts[0];
        }

        // Calculate texture density statistics
        var validTriangles = visData.triangleData.Where(t => t.texelsPerMeter > 0).ToList();
        
        if (validTriangles.Count == 0)
            return null;

        float minTexels = validTriangles.Min(t => t.texelsPerMeter);
        float maxTexels = validTriangles.Max(t => t.texelsPerMeter);
        float meanTexels = validTriangles.Average(t => t.texelsPerMeter);

        return new AtlasInfo
        {
            atlasName = atlasName,
            shaderName = shaderName,
            visData = visData,
            minTexelsPerMeter = minTexels,
            maxTexelsPerMeter = maxTexels,
            meanTexelsPerMeter = meanTexels,
            triangleCount = validTriangles.Count,
            atlasTexture = visData.atlasTexture,
            assetPath = assetPath
        };
    }
}
