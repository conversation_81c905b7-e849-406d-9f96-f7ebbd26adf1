using System;
using System.Collections;
using System.Collections.Generic;
using MACharacterStates;
using UnityEngine;
using UnityEngine.EventSystems;
using Random = UnityEngine.Random;

public class MABuildingSupport : NGCommanderBase
{
#region Fields
        public static Dictionary<int, List<Tuple<Vector3, Vector3>>> m_iconPositions =
        new Dictionary<int, List<Tuple<Vector3, Vector3>>>()
        {
            {
                1,
                new List<Tuple<Vector3, Vector3>>()
                    {new Tuple<Vector3, Vector3>(new Vector3(0, 0, .1f), new Vector3(1, 1, 1))}
            },
            {
                2, new List<Tuple<Vector3, Vector3>>()
                {
                    {new Tuple<Vector3, Vector3>(new Vector3(1, 0, .1f), new Vector3(.5f, .5f, .5f))},
                    {new Tuple<Vector3, Vector3>(new Vector3(-1, 0, .1f), new Vector3(.5f, .5f, .5f))}
                }
            },
            {
                3, new List<Tuple<Vector3, Vector3>>()
                {
                    {new Tuple<Vector3, Vector3>(new Vector3(0f, 1.25f, .1f), new Vector3(.5f, .5f, .5f))},
                    {new Tuple<Vector3, Vector3>(new Vector3(1, 0, .1f), new Vector3(.5f, .5f, .5f))},
                    {new Tuple<Vector3, Vector3>(new Vector3(-1, 0, .1f), new Vector3(.5f, .5f, .5f))}
                }
            },
            {
                4, new List<Tuple<Vector3, Vector3>>()
                {
                    {new Tuple<Vector3, Vector3>(new Vector3(1.25f, 1.25f, .1f), new Vector3(.5f, .5f, .5f))},
                    {new Tuple<Vector3, Vector3>(new Vector3(-1.25f, 1.25f, .1f), new Vector3(.5f, .5f, .5f))},
                    {new Tuple<Vector3, Vector3>(new Vector3(1.25f, -1.25f, .1f), new Vector3(.5f, .5f, .5f))},
                    {new Tuple<Vector3, Vector3>(new Vector3(-1.25f, -1.25f, .1f), new Vector3(.5f, .5f, .5f))}
                }
            },
            {
                5, new List<Tuple<Vector3, Vector3>>()
                {
                    {new Tuple<Vector3, Vector3>(new Vector3(1.25f, 1.25f, .1f), new Vector3(.5f, .5f, .5f))},
                    {new Tuple<Vector3, Vector3>(new Vector3(-1.25f, 1.25f, .1f), new Vector3(.5f, .5f, .5f))},
                    {new Tuple<Vector3, Vector3>(new Vector3(1.25f, -1.25f, .1f), new Vector3(.5f, .5f, .5f))},
                    {new Tuple<Vector3, Vector3>(new Vector3(-1.25f, -1.25f, .1f), new Vector3(.5f, .5f, .5f))},
                    {new Tuple<Vector3, Vector3>(new Vector3(0, 0, .1f), new Vector3(.5f, .5f, .5f))}

                }
            },
        };
#endregion

#region Overrides
    public override Vector3 DoorPosInner
    {
        get
        {
            var buildingNav = GetComponent<BuildingNav>();
            return buildingNav.DoorInterior;
        }
        set
        {
            var buildingNav = GetComponent<BuildingNav>();
            buildingNav.DoorInterior = value;
        }
    }
    public override Vector3 DoorPosOuter
    {
        get
        {
            var buildingNav = GetComponent<BuildingNav>();
            return buildingNav.DoorExterior;
        }
        set
        {
            var buildingNav = GetComponent<BuildingNav>();
            buildingNav.DoorExterior = value;
        }
    }

#endregion
#region Components
     public static void ToggleComponentIcons(Transform _blockHolder, bool _toggle)
     {
         var blocks = _blockHolder.GetComponentsInChildren<Block>();
         foreach (var b in blocks)
         {
            ToggleComponentIcon(b, _toggle);
         }
     }
     public static void ToggleComponentIcon(Block _block, bool _toggle)
     {
         var info = _block.BlockInfo;
         if (info == null) return;
         //Destroy old icons
         var srs = _block.m_toHinges.GetComponentsInChildren<SpriteRenderer>();
         for (int i = srs.Length - 1; i >= 0; i--)
             Destroy(srs[i].gameObject);
         //count valid components
         var componentCount = 0;
         var cSplit = info.m_components.Split(';', ',', '|', '\n');
         foreach(var cs in cSplit)
             if (MAComponentInfo.s_componentInfos.ContainsKey(cs) && MAComponentInfo.s_componentInfos[cs].m_sprite)
                 componentCount++;
         //Setup hinges and places
         if(componentCount == 0) return;
         var hinges = _block.m_toHinges.GetComponentsInChildren<SnapHinge>();
         var places = m_iconPositions[Mathf.Min(5, componentCount)];
         foreach (var hinge in hinges)           // for each hinge on the block
         {
             for (var index = 0; index < cSplit.Length; index++)     //place an icon
             {
                 var c = cSplit[index];
                 if (MAComponentInfo.s_componentInfos.ContainsKey(c) && MAComponentInfo.s_componentInfos[c].m_sprite && index <= componentCount && index < places.Count)
                 {
                     var go = Instantiate(NGManager.Me.m_componentIconPrefab.gameObject, hinge.transform);
                     go.transform.localPosition = places[index].Item1;
                     go.transform.localScale = places[index].Item2;
                     go.GetComponentInChildren<SpriteRenderer>().sprite = MAComponentInfo.s_componentInfos[c].m_sprite;
                 }
             }
         }
     }
#endregion
#region Deliver

     public ReactPickup CreatePickupFromResource(NGCarriableResource _resource, Transform _holder, bool _killPickup, Action<GameObject> _onComplete = null)
     {
        void OnComplete(GameObject _o)
        {
            _o.GetComponent<NGMovingObject>().SetCollisionStyle(NGMovingObject.COLLISIONSTYLE.NOPHYSICS);
            if (_killPickup == false && _o.GetComponent<Pickup>() == null)
            {
                _o.AddComponent<Pickup>();
            }
            _onComplete?.Invoke(_o);
        }
        
         ReactPickupPersistent pickup = null;
         if (_resource.IsProduct)
         {
             var pickupSetup = new PickupSetup();
             pickupSetup.m_quantity = 1;
             pickupSetup.m_killPickup = _killPickup;
             pickupSetup.m_holder = _holder;
             pickupSetup.m_onComplete = OnComplete;
             pickup = NGReactPickupAny.Create(this, _resource, pickupSetup);
         }
         else
         {
             pickup = ReactPickupPersistent.Create(this, _resource, 1, _holder, _killPickup, OnComplete);
         }

         if (pickup == null)
         {
             Debug.LogError($"Failed to create pickup: {_resource.Name}");
             return null;
         }
         return pickup;
     }
     
#endregion
#region Workers
    override public void WorkerArrivesInsideFromOutside(NGMovingObject _worker)
     { 
         Debug.LogError($"Use of Deprecated function WorkerArrivesInsideFromOutside() {_worker.name}");
         ObjectArrivedInside(_worker);
         return;
     }
       
     override public void ObjectArrived(NGMovingObject _object)
     {
     }

     override public SpecialHandlingAction HasSpecialHandling(NGMovingObject _o, SpecialHandlingAction _restrictedAction)
     {
         if (this is MABuilding building == false)
            return null;

         MAWorker worker = _o as MAWorker;
         if (worker != null)
         {
             if ((_restrictedAction == null && worker.Home == null) ||
                 (_restrictedAction == SpecialHandlingAction.AssignHome && (worker.Home == null || worker.Home.Building != building)))
                 if (worker.CanAssignHome && building.GetFreeWorkerBedrooms() > 0)
                     return SpecialHandlingAction.AssignHome;

             if ((_restrictedAction == null && worker.Job == null) ||
                 (_restrictedAction == SpecialHandlingAction.AssignJob))
                 if (worker.CanAssignJob && building.GetFreeWorkerSlots() > 0)
                     return SpecialHandlingAction.AssignJob;
         }

         MAHeroBase hero = _o as MAHeroBase;
         if (hero != null)
         {
             if ((_restrictedAction == null && hero.Home == null) || _restrictedAction == SpecialHandlingAction.AssignHome)
                 if (building.GetFreeHeroBedrooms() > 0)
                     return SpecialHandlingAction.AssignHome;

             if (hero.Home && hero.Home.Building == building)
             {
                 if ((_restrictedAction == null || _restrictedAction == SpecialHandlingAction.GuildHealing) && BCGuildHealing.RequiresHealing(hero))
                 {
                     foreach(var heal in building.BuildingComponents<BCGuildHealing>())
                     {
                         if(heal.IsValid) return SpecialHandlingAction.GuildHealing;
                     }
                 }

                 if ((_restrictedAction == null || _restrictedAction == SpecialHandlingAction.GuildTraining) && hero.HasReachedMaxExperience() == false)
                 {
                    foreach(var train in building.BuildingComponents<BCGuildTraining>())
                    {
                        if(train.IsValid) return SpecialHandlingAction.GuildTraining;
                    }
                 }
             }
         }
         
         foreach (var c in building.m_components)
         {
             var action = c.GetSpecialHandlingAction(_o, _restrictedAction);
             if(action != null) 
                return action;
         }
        return null;
     }

     override public bool ApplySpecialDropHandling(NGMovingObject _o, SpecialHandlingAction _action)
     {
         if (this is MABuilding building)
         {
             if (_action == SpecialHandlingAction.AssignJob)
                 return building.TryAssignJob(_o);
             if (_action == SpecialHandlingAction.AssignHome)
                 return building.TryAssignBedroom(_o);
             if (_action == SpecialHandlingAction.CollectClothing)
             {
                 MAWorker worker = _o as MAWorker;
                 if (worker != null)
                 {
                     worker.SetMoveToBuilding(building, PeepActions.CollectingItem);
                     return true;
                 }
             }
             if(_action == SpecialHandlingAction.CollectWeapon || _action == SpecialHandlingAction.CollectArmour)
             {
                 MAHeroBase hero = _o as MAHeroBase;
                 if(hero != null)
                 {
                     hero.m_destinationMABuilding = building;
                     hero.CharacterUpdateState.ApplyState(CharacterStates.Collect);
                     return true;
                 }
             }
             if(_action == SpecialHandlingAction.GuildTraining)
             {
                 MAHeroBase hero = _o as MAHeroBase;
                 if(hero != null)
                 {
                     hero.CharacterGameState.m_desiredExperienceLevel = hero.CharacterGameState.CharacterLevel+1;
                     hero.m_destinationMABuilding = building;
                     hero.CharacterUpdateState.ApplyState(CharacterStates.GoingHome);
                     return true;
                 }
             }
             if(_action == SpecialHandlingAction.GuildHealing)
             {
                 MAHeroBase hero = _o as MAHeroBase;
                 if(hero != null)
                 {
                     hero.m_destinationMABuilding = building;
                     hero.CharacterUpdateState.ApplyState(CharacterStates.GoingHome);
                     return true;
                 }
             }
         }
         return false;
     }
 
     override public bool ObjectArrivedInside(NGMovingObject _object)
     {
         var building = this as MABuilding;
         building?.SetEntranceCollidersDisabled(false, _object);

         _object.m_mainColliders.FindAll(x => x.gameObject.layer == LayerMask.NameToLayer("BodyToBodyCollider"))
             .ForEach(x => x.enabled = true);
         
         var worker = _object as MAWorker;
         if(WorkerArrivedInside(worker)) return true;

         var hero = _object as MAHeroBase;
         if (HeroArrivedInside(hero)) return true;
          
          var creature = _object as MACreatureBase;
          if(CreatureArrivedInside(creature)) return true;

          var animal = _object as MAAnimal;
          if (AnimalArrivedInside(animal)) return true;
        
          return false;
     }
      
     public bool HeroArrivedInside(MAHeroBase _character)
     {
         if (_character == null) return false;
         var building = this as MABuilding;
         
         if(_character.CharacterUpdateState.State == CharacterStates.Collect)
         {
            building.CharacterCollect(_character);
         }
         else
         {
            if(_character.Home && _character.Home.Building == this && _character.Home.Arrive(_character))
            {
                _character.NGSetAsResting();
                return true;
            }    
         }
         
         _character.CharacterUpdateState.ApplyState(CharacterStates.Spawn);
         return false;
     }
     
    public bool CreatureArrivedInside(MACreatureBase _creature)
    {
         if (_creature == null) return false;
        var building = this as MABuilding;
        
        foreach (var c in building.BuildingComponents<BCTardisCrypt>())
        {
            if (c.Arrive(_creature))
            {
                //TS - Should we win the game (creature enters building) or when building is/was destroyed?
                _creature.DestroyMe();
                return true;
            }
        }
        foreach (var c in building.GetComponentsInChildren<MACreatureSpawnPointActionBase>())
        {
            if (c.Arrive(_creature))
            {
                _creature.DestroyMe();
                //_creature.NGSetAsResting();
                return true;
            }
        }
        
        return false;
    }

    public bool AnimalArrivedInside(MAAnimal _animal)
    {
        if (_animal == null) return false;
        
        _animal.DestroyMe();
        return true;
    }
          
     public bool WorkerArrivedInside(MAWorker _worker)
     {
         if (_worker == null) return false;
         switch (_worker.PeepAction)
         {
             case PeepActions.DeliverToInput:
                 WorkerArrivedDeliver(_worker);
                 break;
             case PeepActions.DeliverToOutput:
                 WorkerArrivedDeliver(_worker);
                 break;
             case PeepActions.ReturnToRest:
                 WorkerArrivedReturnToRest(_worker);
                 break;
             case PeepActions.ReturnToWork:
                 WorkerArrivedReturnToWork(_worker);
                 break;
             case PeepActions.Flee:
                 WorkerArrivedFleeing(_worker);
                 break;
             case PeepActions.WaitingForHome:
                 WorkerArrivedWaitingForHome(_worker);
                 break;
             case PeepActions.Throw:
                 WorkerArrivedThrow(_worker);
                 break;
             case PeepActions.Working:
                 WorkerArrivedWorking(_worker);
                 break;
             case PeepActions.CollectingItem:
                 CharacterCollect(_worker);
                 break;
            case PeepActions.CollectPickup:
                CharacterCollectPickup(_worker);
                break;
            case PeepActions.Despawn:
            case PeepActions.DespawnRun:
                WorkerArrivedDespawn(_worker);
                break;
            default:
                if (_worker is not MAFlowCharacter) // flow characters are script-driven and can arrive without specific PeepActions
                {
                    Debug.LogError(
                        $"{GetType().Name} '{_worker.name}' arrived at building '{name}' with illegal action of {_worker.PeepAction}");
                }
                _worker.SetState(NGMovingObject.STATE.IDLE);
                 break;
         }

         return true;
     }


     public void CharacterCollect(NGMovingObject _o)
     {
         if (this is MABuilding building)
             foreach (var c in building.m_components)
                 if (c is BCActionShop shop && shop.Collect(_o))
                     return;
     }

    public void CharacterCollectPickup(MAWorker _worker)
    {
        if (this is MABuilding building)
        {
            foreach (var c in building.m_components)
            {
                if (c is BCStockBase stockBase)
                {
                    NGCarriableResource resource = stockBase.GetStock().GetHighestStockItem();

                    if (resource != null)
                    {
                        ReactPickup pickup = stockBase.TakePickup(resource, true, (o) => {
                            var item = o.GetComponent<ReactPickupPersistent>();
                            item.AssignToCarrier(_worker);
                        });

                        if (pickup != null)
                        {
                            _worker.SetDefaultAction();
                            return;
                        }
                    }
                }
            }
                
        }      
    }

    public void WorkerArrivedReturnToRest(MAWorker _worker)
     {
         var building = this as MABuilding;
         if (building.WorkerArrivesToRest(_worker) == false)
         {
             Debug.LogError($"{_worker.Name}: has reached {GetBuildingTitle()} with an action {_worker.PeepAction} that is no longer legal");
             _worker.SetState(NGMovingObject.STATE.IDLE);
         }
         else
         {
             _worker.SetState(NGMovingObject.STATE.RESTING);
         }
     }
     public void WorkerArrivedFleeing(MAWorker _worker)
     {
         var building = this as MABuilding;
         if (building.WorkerArrivesToHide(_worker) == false)
         {
             Debug.LogError($"{_worker.Name}: has reached {GetBuildingTitle()} with an action {_worker.PeepAction} that is no longer legal");
             _worker.MASetAsPetrified(null);
         }
         else
         {//TODO: why is this here? WorkerArrivesToXYZ is already doing these
             _worker.PeepAction = PeepActions.Flee;
             _worker.SetState(NGMovingObject.STATE.HIDING);
             _worker.gameObject.SetActive(false);
         }
     }
     public void WorkerArrivedWaitingForHome(MAWorker _worker)
     {
         var building = this as MABuilding;
         if (building.WorkerArrivesWaitingForHome(_worker) == false)
         {
             _worker.SetState(NGMovingObject.STATE.MA_WAITING_FOR_HOME);
             Debug.LogError($"{_worker.Name}: has reached {GetBuildingTitle()} with an action {_worker.PeepAction} that is no longer legal");
         }
     }
     public void WorkerArrivedReturnToWork(MAWorker _worker)
     {
         var building = this as MABuilding;
         if (building.WorkerArrivesToWork(_worker) == false)
         {
             Debug.LogError($"{_worker.Name}: has reached {GetBuildingTitle()} with an action {_worker.PeepAction} that is no longer legal");
             _worker.SetDefaultAction();
         }
         else
         {
             _worker.NGSetAsWorking(false);
         }
     }

     public void WorkerArrivedThrow(MAWorker _worker)
     {
         Debug.Log("Worker Throw");
         _worker.FinishThrow();
     }
     

     public void WorkerArrivedWorking(MAWorker _worker)
     {
         Debug.Log($"{GetType().Name} - WorkerArrivedWorking - {_worker.name}");
         
         _worker.NGSetAsWorking(false);
     }
     
     public void WorkerArrivedDeliver(MAWorker _worker)
     {
         var maBuilding = this as MABuilding;
         var delivered = false;
         if (_worker.Carrying == null || _worker.Carrying.Contents.IsNone)
         {
             _worker.SetDefaultAction();
             return;
         }

         switch (_worker.PeepAction)
         {
             case PeepActions.DeliverToInput:
                 delivered = DeliverToInput(_worker);
                 break;
             case PeepActions.DeliverToOutput:
                 delivered = DeliverToOutput(_worker);
                 break;
             case PeepActions.Throw:
                 _worker.FinishThrow();
                 delivered = false;
                 break;
         }
     }

    private void WorkerArrivedDespawn(MAWorker _worker)
    {
        _worker.DestroyMe();
    }

     private bool CheckSpecialDelivery(MAWorker _worker)
     {
         var building = this as MABuilding;
         foreach (var c in building.m_components)
             if (c is BCResourceNeededBase rnb)
                 if (rnb.OnWorkerDelivers(_worker))
                     return true;
         return false;
     }

     static bool s_disableDelivery = false;
     static DebugConsole.Command s_disableDeliveryCmd = new ("disabledelivery", _s => Utility.SetOrToggle(ref s_disableDelivery, _s));
     private bool DeliverToInput(MAWorker _worker)
     {
         var delivered = false;
         var maBuilding = this as MABuilding;
         if(_worker.Carrying == null)
         {
             if (_worker.Job != null)
             {
                 _worker.SetMoveToComponent(_worker.Job, PeepActions.ReturnToWork);
             }
             _worker.CleanupIfCarrying();
             return false;
         }
         
         var carrying = _worker.Carrying.Contents;
         if(maBuilding.HasSpaceForStock(carrying))
         {
             if (CheckSpecialDelivery(_worker))
                 return true;
             if(s_disableDelivery == false)
             {
                 if (maBuilding.AddPickup(_worker.Carrying))
                 {
                     if (_worker.Job != null)
                     {
                        _worker.SetMoveToComponent(_worker.Job, PeepActions.ReturnToWork);
                     }
                     _worker.CleanupIfCarrying();
                     delivered = true;
                 }
             }
         }

         if (delivered == false)
         {
             Vector3 deliverPos = Vector3.zero;
             if (_worker.m_insideMABuilding != null) 
             {
                 if(_worker.m_insideMABuilding.HasThrowPosition())
                 {
                     deliverPos = ThrowFromPos;
                 }
                 else
                 {
                     deliverPos = _worker.m_insideMABuilding.DoorPosOuter;
                 }
             } 
             else 
             {
                 deliverPos = ThrowFromPos.SetYToHeight();
             }

             _worker.MAThrowCarrying(transform, () => _worker.GetThrowVelocity(this), deliverPos, _worker.MAReturnToJob);
         }
         return delivered;
     }

     bool DeliverToOutput(MAWorker _worker)
     {
         var delivered = false;
         var maBuilding = this as MABuilding;
         if(maBuilding.HasSpaceForStock(_worker.Carrying.Contents))
         {
             if (CheckSpecialDelivery(_worker))
                 return true;
             if(s_disableDelivery == false)
             {
                 if (maBuilding.AddPickup(_worker.Carrying))
                 {
                     if (_worker.Job != null)
                     {
                         _worker.SetMoveToComponent(_worker.Job, PeepActions.ReturnToWork);
                     }
                     _worker.CleanupIfCarrying();
                     delivered = true;
                 }
             }
         }

         if(delivered == false)
         {
             Vector3 deliverPos = Vector3.zero;
             if(_worker.m_insideMABuilding != null)
             {
                 if(_worker.m_insideMABuilding.HasThrowPosition())
                 {
                     deliverPos = ThrowFromPos;
                 }
                 else
                 {
                     deliverPos = _worker.m_insideMABuilding.DoorPosOuter;
                 }
             }
             else
             {
                 deliverPos = ThrowFromPos.SetYToHeight();
             }

             _worker.MAThrowCarrying(transform, () => _worker.GetThrowVelocity(this), deliverPos, _worker.MAReturnToJob);
         }
         return delivered;
     }
     #endregion Workers
#region Utilites
    public Vector3 GetCentalPosition(bool _useMaxY = false)
    {
        var building = this as MABuilding;
        Vector3 pos = Vector3.zero;
        float yMax = 0;
        int count = 0;
        foreach(var comp in building.m_components)
        {
            if(comp == null) continue;
            var colliders = comp.GetComponentsInChildren<Collider>(false);
            if(colliders.Length > 0)
            {
                foreach(var c in colliders)
                {
                    if(c.enabled == false) continue;
                    pos += c.bounds.center;
                    yMax = Mathf.Max(yMax, c.bounds.max.y);
                    ++count;
                }
            }
            else
            {
                yMax = Mathf.Max(yMax, comp.transform.position.y);
                pos += comp.transform.position;
                ++count;    
            }
        }
        var centerPos = pos / (float)count;
        if(_useMaxY)
            centerPos.y = yMax;
        return centerPos;
    }

    public bool DisableContextMenu { get; set; } = false;

	virtual public void TryShowContextMenu()
	{
		if (IsWithinOpenDistrict(true) && !GameManager.IsVisitingInProgress && IsLocked == false && DisableContextMenu == false && GameManager.Me.IsAnyTransitionInProgress == false)
			ContextMenuManager.Me.ShowContextMenu(GetContextMenuData(), transform.position, this);
	}

    override protected List<ContextMenuData.ButtonData> GetContextMenuButtonData()
    {
        var building = this as MABuilding;
        if (IsOperational)
        {
            var buttonDatas = new List<ContextMenuData.ButtonData>
            {
                new ContextMenuData.ButtonData
                {
                    m_label = Localizer.Get(TERM.GUI_INFO),
                    m_audioHook = "InfoButton",
                    m_onClick = ShowInfoPlaque
                }
            };

            if (IsWithinOpenDistrict(true))
            {
                buttonDatas.Add(new ContextMenuData.ButtonData
                {
                    m_label = "Eject Occupants",
                    m_audioHook = "EjectWorkers",
                    m_onClick = building.EjectOccupants,
                    m_interactableCallback = () => IsWithinOpenDistrict(true)
                });
                
                if(IsLocked == false)
                {
                    buttonDatas.Add(new ContextMenuData.ButtonData
                    {
                        m_label = MAUnlocks.CanDesignBuildings ? Localizer.Get(TERM.GUI_DESIGN) : MAMessageManager.GetLockIcon(5, Localizer.Get(TERM.GUI_DESIGN)),
                        m_audioHook = "DesignButton",
                        m_onClick = EnterBuilding,
                        m_interactableCallback = () => MAUnlocks.CanDesignBuildings && IsWithinOpenDistrict(true)
                    });
                    
                    buttonDatas.Add(new ContextMenuData.ButtonData
                    {
                        m_label = MAUnlocks.Me.m_moveBuildings ? "Move Building" : MAMessageManager.GetLockIcon(4, "Move Building"),
                        m_audioHook = "MoveButton",
                        m_onClick = PickUpBuilding,
                        m_interactableCallback = () => MAUnlocks.Me.m_moveBuildings && IsWithinOpenDistrict(true)
                    });
                }
            }
            
            if(building.HasBuildingComponent<BCFactory>() && building.HasActiveOrder)
            {
                var designButton = new ContextMenuData.ButtonData()
                {
                    m_label = "Design Product",
                    m_audioHook = "FactoryDesignProductButton",
                    m_onClick = ChooseProduct,
                    m_interactableCallback = () => IsWithinOpenDistrict(true) 

                };
                buttonDatas.Add(designButton);
            }
            if (building.HasBuildingComponent<BCActionCrypt>() || building.HasBuildingComponent<BCActionGraveyard>())
            {
                buttonDatas.Add(new ContextMenuData.ButtonData()
                {
                    m_label = "Go Underground",
                    m_audioHook = "GoUndergroundButton",
                    m_onClick = () => GameManager.Me.GoUnderground(),
                    m_interactableCallback = () => IsWithinOpenDistrict(true) 
                });
            }
            
            if(Order.IsNullOrEmpty() == false && Order.IsValid)
            {
                buttonDatas.Add(new ContextMenuData.ButtonData()
                {
                    m_label = "Return Order",
                    m_audioHook = "FactoryReturnOrderButton",
                    m_onClick = () => Order?.Return(),
                    m_interactableCallback = () => IsWithinOpenDistrict(true) 
                });
            }
            return buttonDatas;
        }
        return base.GetContextMenuButtonData();
    }

    override protected void ChooseProduct()
    {
        var building = this as MABuilding;
        if (GameManager.Me.IsOKToPlayUISound())
            AudioClipManager.Me.PlaySoundOld("PlaySound_Building_To_DesignTable", GameManager.Me.transform);

        var product = GetProduct();
        if (product == null)
        {
            var productLineString = "Burgers";
            var productLine = building.GetProductLineInfo();
            if (productLine != null)
                productLineString = productLine.m_prefabName;
            DesignTableManager.LoadProductDesign(this, productLineString);
        }
        else
        {
            product.OpenInDesignTable(this);
        }
    }

    private List<Balloon> m_activeBalloons = new List<Balloon>();
    public Balloon CreateBalloon(NGBalloonManager.BalloonType _type, string _balloonText, Sprite _balloonSprite, Action<Balloon> _updateAction, bool _allowMultipules = false, Action<Balloon> _onBalloonClick = null)
    {//TODO: TS - Whoever ends up here please remember balloons do not accept/ cant utilise a sprite.
        var building = this as MABuilding;
        if (_allowMultipules == false)
        {
            //TS - please note: Added a balloonType enum field to balloon because we were comparing two different balloon type enums as ints, one from BalloonCommander and one from BalloonManager.
            //they are different + we then use that as an int index to get a balloon prefab which then introduces its own enum value. caused issues (e.g. 'red' turned to 'timer')
            var existingBalloon = m_activeBalloons.Find(o => o.m_balloonType == _type && o.m_updateAction == _updateAction);
            if(existingBalloon != null)
            {
                existingBalloon.SetText(_balloonText);
                existingBalloon.m_updateAction = _updateAction;
                existingBalloon.m_ClickedBalloonAction = _onBalloonClick;
                return existingBalloon;
            }
        }

        var balloon = NGBalloonManager.Me.CreateBalloon(_type, m_balloonHolder, _balloonText, _balloonSprite, _updateAction, _onBalloonClick);//DestroyedBalloon);
        m_activeBalloons.Add(balloon);
        balloon.SetText(_balloonText); //TS - put this here because text isnt applied properly by CreateBalloon
        balloon.m_updateAction = _updateAction;
        balloon.m_ClickedBalloonAction = _onBalloonClick;
        return balloon;
    }

    public void RemoveBalloon(NGBalloonManager.BalloonType _type)
    {
        int iBalloon = m_activeBalloons.FindIndex(_balloon => (int)_balloon.m_balloonType == (int)_type);
        if(iBalloon != -1)
        {
            Destroy(m_activeBalloons[iBalloon].gameObject);
            m_activeBalloons.RemoveAt(iBalloon);
        }
    }
    
    void DestroyedBalloon(Balloon _balloon)
    {
        m_activeBalloons.Remove(_balloon);
    }
    
    private float debugBalloonTimer;
    private static NGBalloonManager.BalloonType debugBalloonType = 0;

    override protected void SetupBalloons()
    {
        if(m_balloonHolder == null)
        {
            Debug.LogError($"MABuilding - null balloon holder found for building {name} - linUid {m_linkUID}");
        }
        else
        {
            m_balloonHolder.position = LocalHighestPoint;
        }
        //    CreateBalloon(debugBalloonType++, UpdateBalloon);
        //    if (debugBalloonType == NGBalloonManager.BalloonType.Last) debugBalloonType = 0;
        //    debugBalloonTimer = UnityEngine.Random.Range(60f, 60 * 5f) + Time.time;
    }

    void UpdateBalloon(Balloon _balloon)
    {
        _balloon.UpdateBalloonTime((debugBalloonTimer - Time.time).ToHMS());
        if(Time.time >= debugBalloonTimer)
            _balloon.DestroyMe();
    }
    override public GameState_Product ProductMade => GetProduct();
#endregion Utilities
#region Tap&Drag
    public override Vector3 GetBestDropPosition(NGMovingObject _object)
    {
        var reactPickup = _object as ReactPickup;
        if(reactPickup)
        {
            var cmps = (this as MABuilding).m_components;
            // Check for stock ins first
            foreach(var cmp in cmps)
            {
                if(cmp.IsAction) continue;
                if(cmp.GetResourceRequirement(reactPickup.Contents) > 0f)
                    return cmp.transform.position;
                if(_object.DraggedFrom == this && (cmp as BCStockOut)?.GetStockSpace() > 0)
                    return cmp.transform.position;
            }
            // Check actions
            foreach(var cmp in cmps)
            {
                if(cmp.IsAction == false) continue;
                if(cmp.GetResourceRequirement(reactPickup.Contents) > 0f)
                    return cmp.transform.position;
            }
        }
        return DoorPosInner;
    }
    
     override public bool DroppedFromHand(NGMovingObject _object, Vector3 _velocity, Vector3 _dest = default, float flyTime = 1f)
     {
         var maBuilding = this as MABuilding;
         if (_object.IsPeep) return false;
         if (_dest.sqrMagnitude < .001f*.001f) _dest = GetBestDropPosition(_object);
         var velocity = Global3D.GetVelocityRequiredForPointToPointOverTime(_object.transform.position, _dest, flyTime);
         _object.SetCollisionStyle(NGMovingObject.COLLISIONSTYLE.TRIGGER);
         var rb = _object.GetComponentInChildren<Rigidbody>();
         if (rb == null) return false;
         rb.linearVelocity = velocity;
         var pickup = _object as ReactPickup;
         if (pickup)
         {
             pickup.m_intendedDestination = this;
             pickup.m_hasCollidedWith = null;
             //m_playerDelivering += (int) pickup.Quantity;
         }
         _object.OnDrop(); // GL - 111019 - switch shadows back on

         return true;
     }
    
     override public void AcceptThrownDrop(NGMovingObject _obj)
     {
         var building = this as MABuilding;
         if (_obj == null) return;
         var pickup = _obj as ReactPickup;
         if (pickup == null || pickup.gameObject.activeSelf == false) return;
         if (pickup.m_hasCollidedWith == this) return;
         
         pickup.SetCollisionStyle(NGMovingObject.COLLISIONSTYLE.DEFAULT);
         pickup.HasCollidedWith(this);
         
         if(GetDropScore(_obj, out var action, null) <= 0) return;
         if (_obj.IsOkayToCollide(this) == false) return;
         if (building == null) return;
         
         if (GameManager.Me.IsOKToPlayUISound())
             AudioClipManager.Me.PlaySoundOld("PlaySound_DropResource", transform);
             
         if(_obj is NGPickupUpgradeCard) return;
         if(building.AddPickup(pickup) == false)
         {
             Debug.LogError($"MABuildingSupport - AcceptThrownDrop - building.CreateInputStock failed for pickup {pickup.name}");
             return;
         }
         pickup.gameObject.SetActive(false);
         pickup.DestroyMe();

         ShowBuildingAnimation();
     }

     public override float GetDropScore(NGMovingObject _object, out SpecialHandlingAction _action, SpecialHandlingAction _restrictedAction)
     {
         _action = HasSpecialHandling(_object, _restrictedAction);
         if (_action != null)
         {
             return 1f;
         }

         var maBuilding = this as MABuilding;
         var card = _object as NGPickupUpgradeCard;
         if (card)
         {
             NGOrderTile orderTile = card.m_card as NGOrderTile;
             if (orderTile != null)
             {
                 if(maBuilding.HasBuildingComponent<BCFactory>())
                 {
                     if(maBuilding.Order.IsValid == false || orderTile.Order == maBuilding.Order)
                     {
                        return 1;
                     }
                 }
             }
             else if (card.m_gift != null && card.m_gift.m_componentsToUpgrade.IsNullOrWhiteSpace() == false)
             {
                 foreach (var c in card.m_gift.m_componentsToUpgradeInfos)
                 {
                     if (maBuilding.HasBuildingComponent(c.m_classType))
                         return 1;
                 }
             }

             return 0;
         }

         var pickup = _object as ReactPickup;
         if (pickup == null) return 0f;
         
         if(maBuilding.HasSpaceForStock(pickup.Contents, pickup.DraggedFrom == this))
             return 1f;
         return 0f;
     }
     
     override public bool AddObjectFromDrop(NGMovingObject _worker, SpecialHandlingAction _restrictedAction)
     {
         var maBuilding = this as MABuilding;
         var worker = _worker as MAWorker;
         if (worker == null) return false;
         if (worker.Health <= 0) return false;
         
         if (_worker.Carrying)
         {
             worker.SetMoveToBuilding(this, PeepActions.DeliverToInput);
             return true;
         }
         return false;
     }

     public override void ApplyDamageEffect(IDamageReceiver.DamageSource source, float _damageDone, Vector3 _damageOrigin, MAAttackInstance _attack=null, MAHandPowerInfo handPower = null)
     {
         Vector3 forceDir = (transform.position - _damageOrigin).normalized;
         
         var maBuilding = this as MABuilding;

         float actualDamageDone = 0;
         float oldHealth = maBuilding.Health;

         // RW-02-JUL-25: Attacks can do different damage to buildings and creatures/whatever else. 
         if (_attack != null)
         {
             _damageDone = _attack.BuildingDamage;
         }
         
         actualDamageDone = Mathf.Min(Mathf.Clamp(_damageDone, 0, Single.MaxValue), oldHealth);

         float newHealth = oldHealth - actualDamageDone;
         
         _damageDone = actualDamageDone;

         maBuilding.Health = newHealth;
         
         Debug.Log($"{GetType().Name} - {name} - Damage Received worth {actualDamageDone} Defense points. New Defense value {oldHealth}, {maBuilding.Health}");

         if(maBuilding.Health <= 0)
         {
            maBuilding.BuildingDestroyed?.Invoke(maBuilding);

             OnBuildingDestroyed(forceDir);
         }
     }

     protected virtual void OnBuildingDestroyed(Vector3 _forceDirection)
     {
         //TODO: TS - mark building for destruction in case game is quit during this coroutine:
         if(m_buildingDestructionSteps == null)
         {
             m_buildingDestructionSteps = StartCoroutine(BuildingDestruction(this as MABuilding, _forceDirection));
         }
     }
    
     protected Coroutine m_buildingDestructionSteps = null;

     private IEnumerator BuildingDestruction(MABuilding _maBuilding, Vector3 _forceDir)
     {
         // RW-29-JUL-25: Add BuildHelpers to all of the destroyed blocks so the design can easily be recreated.
         var baseBlock = _maBuilding.GetComponentInChildren<BaseBlock>();
         List<Transform> hinges = null;
         List<List<Block>> blocksByPadAndLayer = new();

         if (baseBlock != null)
         {
             hinges = baseBlock.GetHinges();
         }

         for (int i=0; i<hinges.Count; i++)
         {
             blocksByPadAndLayer.Add(new List<Block>());
         }

         Block[] blocks = Visuals.GetComponentsInChildren<Block>();
         foreach (Block block in blocks)
         {
             if(block.name.Contains("MABase"))
                 continue;

             float minXZDistSqd = float.MaxValue;
             int bestPad = -1;
             var blockPos = block.transform.position;
             for (int i=0; i<hinges.Count; i++)
             {
                 float xzSqDistFromHinge = (blockPos - hinges[i].position).xzSqrMagnitude();
                 if (xzSqDistFromHinge < minXZDistSqd)
                 {
                     minXZDistSqd = xzSqDistFromHinge;
                     bestPad = i;
                 }
             }

             if (bestPad >= 0)
             {
                 int layer = 0;
                 for (int i=0; i<blocksByPadAndLayer[bestPad].Count; i++)
                 {
                     if (blockPos.y > blocksByPadAndLayer[bestPad][i].transform.position.y)
                     {
                         layer = i+1;
                     }
                 }

                 blocksByPadAndLayer[bestPad].Insert(layer, block);
             }
         }


         foreach(Block block in blocks) //TODO: TS - This is a performance bottleneck - Start Adding RBs when attack begins until the actual hit because we know the building will be destroyed.
         {
             if(block.name.Contains("MABase"))
                 continue;

             var bh = block.gameObject.AddComponent<BuildHelper>();

             int bestPad = -1;
             int layer = 0;
             for (int i=0; i<blocksByPadAndLayer.Count; i++)
             {
                 for (int j=0; j<blocksByPadAndLayer[i].Count; j++)
                 {
                     if (blocksByPadAndLayer[i][j] == block)
                     {
                         bestPad = i;
                         layer = j;
                     }
                 }
             }

             bh.Set(_maBuilding.m_linkUID, bestPad, layer, 0);

             float generalForceOnDestroyedBlocks = 15f;
                 
             Rigidbody rbBlock = block.gameObject.AddComponent<Rigidbody>();
             rbBlock.collisionDetectionMode = CollisionDetectionMode.Discrete;

             //yield return new WaitForEndOfFrame();
             
             rbBlock.linearVelocity = _forceDir * generalForceOnDestroyedBlocks;
             //rbBlock.angularVelocity = Random.insideUnitSphere * generalForceOnDestroyedBlocks;

             _maBuilding.EjectOccupants();

             DesignTableManager.ConvertToWildBlock(block.gameObject);

             //yield return new WaitForEndOfFrame();
         }

         m_buildingDestructionSteps = null;
         yield break;
     }

#endregion

    public static float GetDistanceScore(float _score, Vector3 _from, Vector3 _to)
    {
        var score = Mathf.Clamp(_score, 0f, 1f);
        //var sqMag = (_to - _from).xzMagnitude();
        //var v3Distance = Vector3.Distance(_to, _from);
        //var sqMagScore = (2f - score) * (_to - _from).xzMagnitude();
        float log2Dist = Mathf.Log((_to - _from).xzMagnitude(), 2);
        if(log2Dist > 0) score = score / log2Dist;
        return score;
    }
    
    public static Tuple<Vector3, Block> FindBuildingHighestPoint(MABuilding _maBuilding)
    {
        Block[] blocks = _maBuilding.GetComponentsInChildren<Block>();

        float highest = _maBuilding.transform.position.GroundPosition().y;
        Block highestBlock = null;
        Vector3 pos = Vector3.zero;
        Vector3 highestPos = Vector3.zero;
        foreach(var block in blocks)
        {
            pos = block.transform.position;
            Bounds bounds = GetBounds(block.gameObject);
            float y = bounds.max.y;
            if(y > highest)
            {
                highest = y;
                highestBlock = block;
                highestPos = pos;
            }
        }

        if(highestBlock == null)
        {
            return new Tuple<Vector3, Block>(_maBuilding.transform.position, null);
        }

        highestPos.y = highest;
        return new Tuple<Vector3, Block>(highestPos, highestBlock);
			
        Bounds GetBounds(GameObject obj)
        {
            Bounds bounds = new();
            Renderer[] renderers = obj.GetComponentsInChildren<Renderer>();
            if(renderers.Length > 0)
            {
                foreach(Renderer renderer in renderers)
                {
                    if(renderer.enabled)
                    {
                        bounds = renderer.bounds;
                        break;
                    }
                }

                foreach(Renderer renderer in renderers)
                {
                    if(renderer.enabled) bounds.Encapsulate(renderer.bounds);
                }
            }

            return bounds;
        }
    }
    public static HashSet<MABuilding> GetBuildingsWithComponent(MAComponentInfo _maComponentInfo, bool _insideOpenDistrict = false)
    {
        var set = new HashSet<MABuilding>();
        if (NGManager.Me)
        {
            if(NGManager.Me.FindBuildingsWithComponent(_maComponentInfo, out var outSet, _insideOpenDistrict))
            {
                if (outSet == null)
                    outSet = new();
                set = outSet;
                return set;
            }
        }
        return set;
    }
    
    public static HashSet<MABuilding> GetBuildingsWithComponents(bool _insideOpenDistrict = false, params MAComponentInfo[] _infos)
    {
        var set = new HashSet<MABuilding>();
        foreach(var info in _infos)
        {
            foreach(var building in GetBuildingsWithComponent(info, _insideOpenDistrict))
            {
                set.Add(building);
            }
        }
        return set;
    }  

    public static HashSet<MABuilding> GetBuildingsWithComponents(List<MAComponentInfo> _infos, bool _insideOpenDistrict = false)
    {
        return GetBuildingsWithComponents(_insideOpenDistrict, _infos.ToArray());
    }

    public static HashSet<MABuilding> GetBuildingsWithComponent<TComponent>(bool _insideOpenDistrict = false) where TComponent : BCBase
    {
        return GetBuildingsWithComponent(typeof(TComponent), _insideOpenDistrict);
    }
    
    public static HashSet<MABuilding> GetBuildingsWithComponent(Type _type, bool _insideOpenDistrict = false)
    {
        MAComponentInfo maComponentInfo = MAComponentInfo.GetInfoByClass(_type);
        return GetBuildingsWithComponent(maComponentInfo, _insideOpenDistrict);
    }
    
    public static HashSet<MABuilding> GetBuildingsWithComponents(bool _insideOpenDistrict = false, params Type[] _types)
    {
        var set = new HashSet<MABuilding>();
        foreach(var type in _types)
        {
            foreach(var building in GetBuildingsWithComponent(type, _insideOpenDistrict))
            {
                set.Add(building);
            }
        }
        return set;
    }
}
