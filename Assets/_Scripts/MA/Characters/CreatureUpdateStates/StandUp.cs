using System;
using UnityEngine;

namespace MACharacterStates
{
	[Serializable]
	public class StandUp : CommonState
	{
		private float m_exitStateElapsedTime = 0f;
		
		public StandUp(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		public override void OnEnter()
		{
			base.OnEnter();

			//TS - unfortunately we have to check if this is a worker-type, the state coulud be MA_DECIDE_etc
			MAWorker workerType = m_character as MAWorker;
			m_character.SetState(workerType != null ? workerType.InitialWorkerState : NGMovingObject.STATE.IDLE);

			var rc = m_character.m_ragdollController;
			if (rc != null)
			{
				rc.PauseNextStateChange = false;

				m_character.m_nav.PushPause("StandUp", false, true);

				if (!rc.IsRagdolled || rc.WillChangeState)
				{
					OnUp(true);
					return;
				}
				m_character.SetCanBePushed(false);
				m_character.m_ragdollController.AddRagDollEndedListener(OnUp);
			}
			else
			{
				OnUp(false);
			}
		}

		public override void OnUpdate()
		{
			base.OnUpdate();

			if (!m_character.m_ragdollController.IsRagdolled)
			{
				if (m_gameStateData.m_timeInState >= 3.5f)
				{
					m_exitStateElapsedTime = 0f;
					OnUp(true);
				}
				else
				{
					m_exitStateElapsedTime += Time.deltaTime;
				}
			}
		}

		public override void OnExit()
		{
			base.OnExit();

			m_exitStateElapsedTime = 0f;

			if (m_character.m_ragdollController != null)
			{
				m_character.m_ragdollController.RemoveRagDollEndedListener(OnUp);
				m_character.m_bodyToBodyCollider.enabled = true;
				m_character.m_nav.PopPause("StandUp");
				m_character.SetCanBePushed(true);
			}
		}

		private void OnUp(bool _interrupted)
		{
			if (m_character.m_ragdollController != null)
			{
				m_character.m_ragdollController.RemoveRagDollEndedListener(OnUp);
			}

			MAWorker worker = m_character as MAWorker;
			if (worker != null && worker.BackUpMovementState != NGMovingObject.STATE.NONE)
			{
				worker.SetState(worker.BackUpMovementState);
				worker.GameStatePerson.m_backupMovementState = (int)NGMovingObject.STATE.NONE;
			}
			
			string newstate = GetAttackStateForTarget();
			if (m_character.StateLibrary().ContainsKey(newstate))
			{
				ApplyState(newstate);
			}
			else
			{
				Debug.Log($"Character: ' {m_character.name} 'StandUp: setting to state '{CharacterStates.Idle.ToString()}' - attempted state state '{newstate}' unavailable for this character");
				ApplyState(CharacterStates.Idle);
			}
		}
	}
}