using System;
using System.Collections.Generic;
using UnityEngine;

[Serializable]
public class CharacterExperience
{
	public const int c_maxLevel = 400;
	private float c_baseExp = 100f;
	private float c_exponentialGrowthFactor = 1.5f;
	public float m_experienceMultiplier = 1f;
	public SDictionary<int,string> m_rewardsGainedMap = new();
	
	//public List<string> m_rewardsGained = new List<string>();
	
	public bool RewardMatches(int _level, string _reward)
	{
		if(m_rewardsGainedMap.TryGetValue(_level, out var result))
		{
			if(result == null) return false;
			
			return result.Equals(_reward);
		}
		return false;
	}
	
	public bool HasReward(int _level)
	{
		if(m_rewardsGainedMap.TryGetValue(_level, out var result))
		{
			return result.IsNullOrWhiteSpace() == false;
		}
		return false;
	}
	
	public void AssignReward(int _level, string _reward)
	{
		m_rewardsGainedMap[_level] = _reward;
	}
	
	public bool CanLevelup()
	{
		return HasReward(m_level) == false;
	}
	/*public float ExperienceRequiredForLevel(int _level)
	{
		float xpRequired = 0;
		if (_level > 1)
		{
			// I'm making it so progress from Level 1 to Level 2 (the first level-up) is 
			// c_baseExp. So the exponent comes out as 0 when checking L2 and it goes from there.
			xpRequired = c_baseExp * Mathf.Pow(c_exponentialGrowthFactor, _level-2);
			xpRequired += ExperienceRequiredForLevel(_level-1);
		}
		return xpRequired;
	}*/

	// public float ExperienceRequiredForNextLevel()
	// {
	// 	return ExperienceRequiredForLevel(m_level+1);
	// }

	// public void RecalculateLevel()
	// {
	// 	return;
	// 	m_level = 1;
	// 	while (m_level < c_maxLevel && m_experience >= ExperienceRequiredForLevel(m_level+1))
	// 	{
	// 		m_level++;
	// 	}
	// }
	
	public float m_experience;
	public int m_level;
//	public float m_experienceThisLevel;
	
	public List<ExperienceLog> m_experienceLog = new();

	// These need to be refined and perhaps inserted into knack?
	public string GetCharacterLevelName()
	{
		switch(m_level)
		{
			case 0:
			case 1: return "Fledgling";
			case 2: return "Journeyman";
			case 3: return "Seasoned";
			case 4: return "Veteran";
		}
		return "TODO";
	}
	public int CharacterLevel => m_level;// Mathf.FloorToInt(LevellingFormula(m_experience));

	public CharacterExperience()
	{
		m_level = 0;
		m_experience = 0;
	}
	
	public void AddExperience(float _experienceValue, string _reason)
	{
		m_experience += _experienceValue;
//		m_experienceThisLevel += _experienceValue;

		//RecalculateLevel();
		LogExperience(_experienceValue, _reason);
	}

	public void LogExperience(float _experienceValue, string _reason)
	{
		if (m_experienceLog == null) m_experienceLog = new();
		
		_reason = _reason.RemoveWhiteSpaceAndToLower();
		int iMostRecentEntry = m_experienceLog.Count - 1;
		if (m_experienceLog.Count == 0 || m_experienceLog[iMostRecentEntry].Reason != _reason)
		{
			ExperienceLog charXPLog = new ExperienceLog(_experienceValue, _reason);
			m_experienceLog.Add(charXPLog);
			iMostRecentEntry = m_experienceLog.Count - 1;
		}
		m_experienceLog[iMostRecentEntry].m_experienceValueAdded += _experienceValue;
		m_experienceLog[iMostRecentEntry].m_timesAdded++;	
	}
	

	// protected float LevellingFormula(float _experience)
	// {
	// 	return 1 + Mathf.Sqrt(_experience);
	// }

	//just for logging how and why we gained experience
	[Serializable]
	public class ExperienceLog
	{
		static readonly List<(string reasonName, byte reasonId)> s_experienceTypes = new()
		{
			("fight", 1), ("kill", 2), ("possessedmove", 3), ("training", 4), ("debug", 10)
		};

		public float m_experienceValueAdded;
		public int m_timesAdded;
		public byte m_reason;

		public ExperienceLog(float _experienceValueAdded, string _reason)
		{
			m_experienceValueAdded = _experienceValueAdded;
			int i = s_experienceTypes.FindIndex(x => x.reasonName == _reason);
			if (i > -1)
				m_reason = s_experienceTypes[i].reasonId;
			else m_reason = byte.MaxValue;
		}
		
		public string Reason
		{
			get
			{
				int i = s_experienceTypes.FindIndex(x => x.reasonId == m_reason);
				if (i > -1)
					return s_experienceTypes[i].reasonName;
				else return "";
			}
		}
	}
}