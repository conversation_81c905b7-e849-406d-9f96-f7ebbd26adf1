using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class ArmourHealth : IHealthBar
{
    public float MaxHealth { get; set; }
    public float MaxArmour { get; set; }
    public float NormalizedHealth { get; set; }
    public float NormalizedArmour { get; set; }
    public bool ShowHealthBar { get; set; }
    public int Level { get; set; }
    public float HealthBarHeight { get; set; }
}

public class MADesignGuage : MonoBehaviour
{
    public HealthBarElement m_armorBar;
    private NGDesignInterface.DesignScoreInterface m_score;
    public RectTransform m_needle;
    public RectTransform m_salesPriceMultiplierHolder;
    public TMP_Text m_salesPriceMultiplier;
    public RectTransform m_bar;
    public Transform m_textHolder;
    public TMP_Text m_DebugScoreText;
    private int m_lastScoreCategory = -1, m_lastScoreItemUniqueIdentifier = -1;
    private ArmourHealth m_armour = new ArmourHealth();
    
    void Start()
    {
        Activate();
    }

    private void OnEnable()
    {
        if (DesignTableManager.Me == null)
            return;
        Activate();
    }

    void Update()
    {
        if (DesignTableManager.Me == null)
            return;
        Activate();
    }
    
    public static Dictionary<GaugeType, string[]> s_gaugeNames = new ()
    {
        { GaugeType.None, new [] { "Poor", "Mediocre", "Decent", "Excellent", "Exquisite" } },
        { GaugeType.Defense, new [] { "Flimsy", "Thin", "Sturdy", "Strong", "Impenetrable" } },
        { GaugeType.Attack, new [] { "Feeble", "Weak", "Potent", "Mighty", "Lethal" } },
        { GaugeType.Nutrition, new [] { "Basic", "Balanced", "Nourishing", "Tasty", "Delicious" } },
        { GaugeType.Beauty, new [] { "Plain", "Simple", "Attractive", "Beautiful", "Stunning" } },
        { GaugeType.DifficultyToMake, new [] { "Effortless", "Straightforward", "Manageable", "Complex", "Challenging" } },
        { GaugeType.Color, new [] { "#c22d00", "#db7900", "#bfac00", "#9bde00", "#03cc00" } }
    };

    public enum GaugeType
    {
        None,
        Attack,
        Defense,
        Nutrition,
        Beauty,
        DifficultyToMake,
        Color,
    }
    
    public static MAOrderDataManager.QualityEntry GetQualityEntry(string _productLine, float _value)
    {
        _value = Mathf.Clamp01(_value);
        var index = GetIndexFromScore(_value);

        var gaugeType = GetGaugeType(_productLine);
        
        var qualityEntry = new MAOrderDataManager.QualityEntry();
        qualityEntry.m_color = HexToColor(s_gaugeNames[GaugeType.Color][index]);
        qualityEntry.m_colorHex = s_gaugeNames[GaugeType.Color][index];
        qualityEntry.m_name = s_gaugeNames[gaugeType][index];
        return qualityEntry;
    }
    
    public static Color HexToColor(string hex)
    {
        hex = hex.Replace ("0x", "");//in case the string is formatted 0xFFFFFF
        hex = hex.Replace ("#", "");//in case the string is formatted #FFFFFF
        byte a = 255;//assume fully visible unless specified in hex
        byte r = byte.Parse(hex.Substring(0,2), System.Globalization.NumberStyles.HexNumber);
        byte g = byte.Parse(hex.Substring(2,2), System.Globalization.NumberStyles.HexNumber);
        byte b = byte.Parse(hex.Substring(4,2), System.Globalization.NumberStyles.HexNumber);
        //Only use alpha if the string has enough characters
        if(hex.Length == 8){
            a = byte.Parse(hex.Substring(6,2), System.Globalization.NumberStyles.HexNumber);
        }
        return new Color32(r,g,b,a);
    }
    
    public static string GetNameValue(string _productLine, float _value, bool _color = true)
    {
        _value = Mathf.Clamp01(_value);
        var gaugeType = GetGaugeType(_productLine);
        return GetNameValue(gaugeType, _value, _color);
    }
    
    public static string GetNameValue(GaugeType _type, float _value, bool color = true)
    {
        _value = Mathf.Clamp01(_value);
        var names = s_gaugeNames[_type];
        var index = GetIndexFromScore(_value);
        
        if(color)
        {
            int colorIndex = _type == GaugeType.DifficultyToMake ? 4 - index : index;
            return $"<color={s_gaugeNames[GaugeType.Color][colorIndex]}>{names[index]}</color>";
        }
        return names[index];
    }
    
    public static GaugeType GetGaugeType(string _productLine)
    {
        if(_productLine == null) return GaugeType.Beauty;
        
        if(_productLine.IsNullOrWhiteSpace() == false)
        {
            switch(_productLine.ToLower())
            {
                case "ammo":
                case "weapons": 
                case "projectile":
                    return GaugeType.Attack;
                case "food": return GaugeType.Nutrition;
                case "armour": return GaugeType.Defense;
                case "armour female": return GaugeType.Defense;
                case "clothing": return GaugeType.Beauty;
                case "clothing female": return GaugeType.Beauty;
            }
        }
        return GaugeType.None;
    }
    
    public void UpdateNames(string _productLine)
    {
        GaugeType type = GetGaugeType(_productLine);
        
        var names = s_gaugeNames[type];
        var texts = m_textHolder.GetComponentsInChildren<TMP_Text>();
        for(int i = 0; i < texts.Length; ++i)
        {
            texts[i].text = names[i];
        }
    }
    
    public void ResetLastCategory() => m_lastScoreCategory = -1;
    
    void Activate()
    {
        bool active = DesignTableManager.Me.IsInDesignInPlace;
        
        if(m_bar.gameObject.activeSelf != active)
            m_bar.gameObject.SetActive(active);
            
        m_score = DesignTableManager.Me.DesignInterface;
        
        Init();
        
        var score = m_score.TotalScore;
        var clampedScore = Mathf.Clamp01(score);
        var width = m_bar.rect.width;
        m_salesPriceMultiplierHolder.gameObject.SetActive(m_score.IsProduct);
        m_salesPriceMultiplier.text = m_score.SalesPriceMultuiplierText;
        m_armour.NormalizedArmour = score / m_armour.MaxArmour;
        m_needle.anchoredPosition = new Vector2(width*clampedScore, m_needle.anchoredPosition.y);
        m_salesPriceMultiplierHolder.anchoredPosition = new Vector2(width*clampedScore, m_salesPriceMultiplierHolder.anchoredPosition.y);
        var texts = m_textHolder.GetComponentsInChildren<TMP_Text>(); 
        //foreach(var t in texts) t.fontStyle = FontStyles.Normal; 
        var i = (int)(texts.Length * Mathf.Clamp(score,0f, 1f-.0001f));
        texts[i].fontStyle = FontStyles.Bold | FontStyles.Underline; 
        if (m_DebugScoreText != null)
            m_DebugScoreText.text = $"Score={score:F3}";
        UpdateNames(m_score?.ProductLine?.m_prefabName.ToLower());
        UpdateAudio(score);
    }
    
    private void Init()
    {
        string produceLine = m_score?.ProductLine?.m_prefabName.ToLower();
        
        switch(produceLine)
        {
            case "armour": 
                m_armorBar?.gameObject.SetActive(true);
                m_armour.MaxArmour = 100;
                m_armorBar?.Init(m_armour);
                break;
            default:
                m_armorBar?.gameObject.SetActive(false);
                break;
        }
    }

    void UpdateAudio(float _score)
    {
        int itemUniqueIdentifier = DesignTableManager.Me.ItemUniqueIdentifier;
        if (m_lastScoreItemUniqueIdentifier != itemUniqueIdentifier)
        {
            m_lastScoreItemUniqueIdentifier = itemUniqueIdentifier;
            m_lastScoreCategory = -1; // item on table changed, don't play audio
        }
        int newDesignScoreCategory = GetIndexFromScore(_score);
        if (m_lastScoreCategory >= 0 && m_lastScoreCategory != newDesignScoreCategory)
        {
            var audio = newDesignScoreCategory > m_lastScoreCategory ? "PlaySound_DesignGaugeIncrease" : "PlaySound_DesignGaugeDecrease";
            var level = c_gaugeAudioLevels[newDesignScoreCategory];
            AudioClipManager.Me.SetSoundSwitch("DesignGauge", $"DesignGauge_{level}", GameManager.Me.gameObject);
            AudioClipManager.Me.PlaySound(audio, GameManager.Me.gameObject);
        }
        m_lastScoreCategory = newDesignScoreCategory;
    }

    public static int GetIndexFromScore(float _score)
    {
        return Mathf.Clamp((int) (_score * 5), 0, 4);
    }
    
    static string[] c_gaugeAudioLevels =
    {
        "Poor", "Mediocre", "Decent", "Excellent", "Exquisite"
    };
    
    public static MADesignGuage Create(Transform _holder, NGDesignInterface.DesignScoreInterface _score)
    {
        var go = Instantiate(NGManager.Me.m_MADesignGuagePrefab, _holder);
        var mg = go.GetComponent<MADesignGuage>();
        mg.Activate();
        return mg;
    }
}
