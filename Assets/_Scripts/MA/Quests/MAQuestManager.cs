using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;

public class MAQuestManager : Mono<PERSON>ingleton<MAQuestManager>
{
    public Transform m_questHolder;
    public List<MAQuestBase> m_activeQuests = new List<MAQuestBase>();
    public void AddQuest(MAQuestBase _quest)
    {
        if(m_activeQuests.Contains(_quest) == false)
            m_activeQuests.Add(_quest);
    }
    public bool IsActive(MAQuestBase _quest)
    {
        return m_activeQuests.Contains(_quest);
    }
    public bool IsActive(string _id)
    {
        var found = m_activeQuests.Find(o => o.m_id == _id);
        if(found != null) return true;
        var foundV2 = m_activeQuests.Find(o => o.m_id == _id);
        return found != null;
    }
    public MAQuestBase GetQuest(string _id)
    {
        var found = m_activeQuests.Find(o => o.m_id.Equals(_id, StringComparison.InvariantCultureIgnoreCase));
        return found;
    }
    public MAQuestBase GetQuestBase<T>() where T : MAQuestBase
    {
        var found = m_activeQuests.Find(o => o.GetType() == typeof(T));
        return found;
    }
    public void RemoveQuest(MAQuestBase _quest)
    {
        if(m_activeQuests.Contains(_quest))
            m_activeQuests.Remove(_quest);
    }
    public class SaveQuestManager
    {
        [Save] public List<string> m_activeQuests = new List<string>();
        public string Save()
        {
            var s = "";
            foreach (var q in m_activeQuests)
            {
                s += q;
            }
            return s;
        }
    }

    public class SaveLoadQuests
    {
        [Save] public List<MAQuestBase.SaveLoadQuestBaseContainer> m_allQuests = new ();
    }
    public string SaveAll()
    {
        var saveLoadQuests = new SaveLoadQuests();
        foreach(var q in m_activeQuests)
        {
            saveLoadQuests.m_allQuests.Add(q.Save()); ;
        }
        var serialise = SSerializer.Serialize(saveLoadQuests);
        return serialise;
    }
    
    public void LoadAllQuests(string _l)
    {
        if (_l.IsNullOrWhiteSpace()) return;
        var deseralise = SSerializer.Deserialize<SaveLoadQuests>(_l);
        foreach(var l in deseralise.m_allQuests)
        {
            if(l == null) continue;
            var q = m_activeQuests.Find(o => o.m_id == l.m_id);
            if(q != null)
            {
                q.Load(l);
            }
        }
    }

    public void OnDisplayMessage(MAMessage _message)
    {
        foreach(MAQuestBase quest in m_activeQuests)
        {
            quest.OnDisplayMessage(_message);
        }
    }

    public void OnDestroyMessage(MAMessage _message)
    {
        foreach (MAQuestBase quest in m_activeQuests)
        {
            quest.OnDestroyMessage(_message);
        }
    }
        
    public List<MABuilding> RequiredPossessableBuildings()
    {
        List<MABuilding> required = new();
        foreach (MAQuestBase meActiveQuest in MAQuestManager.Me.m_activeQuests)
        {
            if (meActiveQuest.Status == MAQuestBase.QuestStatus.InProgress)
            {
                required.AddRange(meActiveQuest.GetPossessableBuildings());
            }
        }
        return required;
    }  
    
    public List<NGMovingObject> RequiredPossessableMovingObjects()
    {
        List<NGMovingObject> required = new();
        foreach (MAQuestBase meActiveQuest in MAQuestManager.Me.m_activeQuests)
        {
            if (meActiveQuest.Status == MAQuestBase.QuestStatus.InProgress)
            {
                required.AddRange(meActiveQuest.GetPossessableMovers());
            }
        }

        return required;
    }
}
