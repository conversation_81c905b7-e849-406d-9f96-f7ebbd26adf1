using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngineInternal;
using Random = UnityEngine.Random;

public class MAQuestChickenFootball : MAQuestBase
{
    public MAChickenFootballPen m_chickenFootballPen;
    private const string m_animalType = "QuestChicken";
    public static int m_chickenToReturn = 10;
    private MACreatureInfo CreatureInfo => MACreatureInfo.GetInfo(m_animalType);

    private int m_chickenSpawnCount = 18;
    private float m_minSpawnRadus = 18.0f;
    private float m_maxSpawnRadus = 26.0f;

    private List<MAChicken> m_chickens = new List<MAChicken>();

    public float m_moveChance = 0.5f;
    public float m_CheckChickenFrequency = 5.0f;
    private float m_CheckChickenTimer = 0.0f;

    private MAFlowCharacter m_chickenFarmer;

    protected override void OnPostLoad()
    {
        InitReferences();
    }

    private void InitReferences()
    {
        if (m_chickenFarmer == null)
        {
            m_chickenFarmer =
                NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == "ChickenFarmer") as MAFlowCharacter;
        }

        if (m_chickenFarmer != null)
        {
            m_lipSyncers["ChickenFarmer"] = m_chickenFarmer;

            var ls = m_chickenFarmer.SetLipSync(false);
            ls.m_deltaRot = -24.0f;
        }
    }

    private void Update()
    {
        if (m_status == QuestStatus.InProgress)
        {
            CheckAndMoveChickens(m_chickenFootballPen.transform.position, m_minSpawnRadus, m_maxSpawnRadus);
        }
    }

    public void SpawnChickens()
    {
        for (int i = 0; i < m_chickenSpawnCount; i++)
        {
            Vector3 position = GetRandomSpawnPosition(m_chickenFootballPen.transform.position, m_minSpawnRadus, m_maxSpawnRadus);
            var character = MACreatureControl.Me.SpawnNewCreatureAtPosition(CreatureInfo, position);
            MAChicken chicken = character as MAChicken;
            
            chicken.m_nav.AllowStuck(true);
            chicken.SetKickVelocity(6.0f);
            m_chickens.Add(chicken);
            chicken.CharacterGameState.m_immortal = true;
        }
    }
    
    private Vector3 GetRandomSpawnPosition(Vector3 _center, float _minRadius, float _maxRadius)
    {
        Vector2 randomDirection = Random.insideUnitCircle.normalized;
        float distance = Random.Range(_minRadius, _maxRadius);
        Vector3 offset = new Vector3(randomDirection.x, 0, randomDirection.y) * distance;
        return _center + offset;
    }
    
    private void CheckAndMoveChickens(Vector3 _centre, float _minRadius, float _maxRadius)
    {
        m_CheckChickenTimer += Time.deltaTime;    
        if(m_CheckChickenTimer < m_CheckChickenFrequency)
            return;
        m_CheckChickenTimer = 0.0f;
            
        foreach (var chicken in m_chickens)
        {
            if (m_chickenFootballPen.IsChickenPenned(chicken))
                continue;

            Vector3 chickenPos = chicken.transform.position;
            float distance = Vector3.Distance(chickenPos, _centre);

            if (distance < _minRadius || distance > _maxRadius)
            {
                if (Random.value < m_moveChance)
                {
                    
                    Vector3 direction = (chickenPos - _centre).normalized;
                    float randomDistance = Random.Range(_minRadius, _maxRadius);

                    Vector3 newPosition = _centre + direction * randomDistance;
                    chicken.SetMoveToPosition(newPosition);
                }
            }
        }
    }

    private bool FirstChickenReturned()
    {
        return m_chickenFootballPen.ChickenCount > 0;
    }  
    
    private bool OneChickenLeft()
    {
        return m_chickenFootballPen.ChickenCount == m_chickenToReturn - 1;
    }  
    
    public override float QuestObjectiveValue(string _objective)
    {
        if (_objective.Contains("ReturnChickens"))
        {
            float ChickenToReturn = Math.Clamp(m_chickenToReturn - m_chickenFootballPen.ChickenCount, 0, m_chickenToReturn);
            return ChickenToReturn;
        }

        return 1.0f;
    }
    
    public override void TriggerQuestEvent(string _event)
    {
        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("SpawnChickens"))
        {
            SpawnChickens();
        }
    }
    
    public override bool WaitForQuestEvent(string _event)
    {
        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("FirstChickenReturned"))
        {
            return FirstChickenReturned();
        }
        if (split[0].Contains("OneChickenLeft"))
        {
            return OneChickenLeft();
        }

        return false;
    }
}
