using MACharacterStates;
using UnityEngine;

public class MAQuestRumBarrelChicken : MonoBehaviour
{
    public Vector3 m_despawnPosition = Vector3.zero;
    private const string m_animalType = "QuestChicken";
    private MACreatureInfo CreatureInfo => MACreatureInfo.GetInfo(m_animalType);

    private void SendChickenFleeing()
    {
        var character = MACreatureControl.Me.SpawnNewCreatureAtPosition(CreatureInfo, transform.position);
        MAChicken chicken = character as MAChicken;
        
        if (chicken != null)
        {
            chicken.CharacterGameState.m_walkSpeed = chicken.CharacterGameState.m_attackSpeed;
            chicken.SetSpeed(chicken.CharacterGameState.m_attackSpeed);
            
            chicken.m_interactSound?.Play(chicken.gameObject);
            
            Vector3 despawnPos = m_despawnPosition == Vector3.zero
                ? transform.position + new Vector3(20, 0, 20)
                : m_despawnPosition;
            chicken.SetDespawnPosition(despawnPos);
            MACharacterStateFactory.ApplyCharacterState(CharacterStates.GoingHome, chicken);
        }
    }

    private void Awake()
    {
        SendChickenFleeing();
    }
}
