using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MAQuestGiver : <PERSON><PERSON><PERSON><PERSON>, ICharacterObjectInteract
{
    [Header("Quest Giver")]
    public Transform m_questScrollHolder;
    public MAQuestScroll m_questScroll;
    public List<string> m_animationStringsToPlay = new List<string>();
    public bool m_canBeTargeted = false;
    private float m_stopLoopingAnimationAfter = -1;
    public override KeyboardShortcutManager.EShortcutType KeyboardShortcut => KeyboardShortcutManager.EShortcutType.HeldObjects;
    
    public string InteractType => null;
    public bool CanInteract(NGMovingObject _chr) => m_questScroll != null && m_questScroll.CanInteract(_chr);
    public string GetInteractLabel(NGMovingObject _chr) => MAQuestScroll.InteractLabel;
    public void DoInteract(NGMovingObject _chr) { if(m_questScroll != null) m_questScroll.DoInteract(_chr); }
    public bool RunInteractSequence(List<FollowChild> _chr) => false;
    public void EnableInteractionTriggers(bool _b) {}
    public float AutoInteractTime => 0;
    
    #region Overrides
    public override EDeathType DeathCountType => EDeathType.None;
    public override bool CanPickup() { return false; }
    public override bool CanBeTargeted { get { return m_canBeTargeted; } }
    public override bool CanAssignHome => false;
    public override bool CanAssignJob => false;
    
    override protected GameObject CharacterPrefab => (m_workerInfo == null || m_workerInfo.m_characterPrefab == null) ? GlobalData.Me.m_touristPrefab : m_workerInfo.m_characterPrefab;

    protected override void Awake()
    {
        m_initialWorkerState = STATE.MA_DECIDE_WHAT_TO_DO;
        
        base.Awake();
    }

    override protected void Start()
    {
        base.Start();
        SetState(STATE.MA_DECIDE_WHAT_TO_DO);

        if (m_questScrollHolder == null)
        {
            m_questScrollHolder = transform.Find("QuestScrollHolder");
        }
    }
    override protected void OnDestroy()
    {
        Debug.Log("Quest Giver Destroy State");
        base.OnDestroy();
    }

    override protected void UpdateState()
    {
        switch(m_state)
        {
            case STATE.MA_DECIDE_WHAT_TO_DO:
                if(UpdateThreatAlarm() || UpdateTargetedByNearbyCreature())
                    return;
                break;
            case STATE.MA_LEAVING:
                if (StateMoveToPosition())
                {
                    DestroyMe();
                }
                break;
            default:
                base.UpdateState(); 
                break;
            // case STATE.MA_CHASE_OBJECT:
            // case STATE.MA_MOVE_TO_POSITION:
            // case STATE.MA_MOVE_TO_BUILDING:
            //     break;
        }
        //Debug.Log("Quest Giver Update State");
    }
    override public void SetDefaultAction()
    {
        SetState(STATE.MA_DECIDE_WHAT_TO_DO);
    }
    override protected bool StateMoveToBuilding()
    {        
        if (base.StateMoveToBuilding() == false) return false;
        SetState(STATE.MA_DECIDE_WHAT_TO_DO);
        return true;
    }
    override protected bool StateMoveToPosition()
    {
        if (m_nav && HasAgentArrived())
        {
            switch (PeepAction)
            {
                case PeepActions.Despawn:
                case PeepActions.DespawnRun:
                    DestroyMe();
                    return true;
            }
            
            if(UpdateThreatAlarm() || UpdateTargetedByNearbyCreature())
                return false;
                
            return PlayNextAnimation();
        }

        if(UpdateThreatAlarm() || UpdateTargetedByNearbyCreature())
            return false;
            
        return false;
    }
    override public void NGSetAsWorking(bool _isVisibleInside)
    {
        PeepAction = PeepActions.Working;
        SetState(STATE.WORKING);
        gameObject.SetActive(_isVisibleInside);
	    
        IEnumerator StayInsideFor()
        {
            yield return new WaitForSeconds(3f);
            SetMoveToPosition(m_insideMABuilding.DoorPosOuter, false, PeepActions.Working);
        }
	    
        MATouristManager.Me.StartCoroutine(StayInsideFor());
    }
    override public void DestroyMe()
    {
        GameManager.Me.m_state.m_people.Remove(GameState as GameState_Person);
        base.DestroyMe();
    }
   
    override protected void CommonUpdateState()
    {
        if(m_state != STATE.MA_DEAD && Health <= 0)
        {
            SetDead();
        }
    }
    #endregion Overrides

    bool PlayNextAnimation()        //Not sure if we need this. Only there to support StateMoveToPosition()  &  StateMoveToBuilding()
    {
        if(m_state == STATE.MA_DEAD)
        {
            return true;
        }
        if(m_animationStringsToPlay.Count == 0)
        {
            SetState(STATE.MA_DECIDE_WHAT_TO_DO);
            return true;
        }
	    
        // Looping animations will never finish on their own
        var clip = AnimationOverride.LookupClip(m_animationStringsToPlay[0], out var attachData, out var blendTime);
        if(clip.isLooping)
        {
            m_stopLoopingAnimationAfter = clip.length * Random.Range(1,10);
        }
        PlaySingleAnimation(m_animationStringsToPlay[0], (c) => { PlayNextAnimation(); });
        m_animationStringsToPlay.RemoveAt(0);
        SetState(STATE.MA_WAITING_FOR_ANIMATION);
        return false;
    }
    
    public override string GetDefaultDisplayName()
    {
        return m_workerInfo?.m_displayName ?? "";
    }
    
    public void CreateScroll(System.Action _onClickedScroll, Transform _overrideScrollHolder, MAQuestBase.QuestInteractType _questInteractType)
    {
        if(m_questScroll != null) return;

        if(_overrideScrollHolder != null)
        {
            m_questScrollHolder = _overrideScrollHolder;
        }

        m_questScroll = MAQuestScroll.Create(m_questScrollHolder, MAQuestBase.QuestStatus.Active,  _onClickedScroll, _questInteractType);
        m_questScroll.gameObject.SetActive(true);
    }
    public void ToggleQuestScroll(bool _active)
    {
        m_questScrollHolder?.gameObject.SetActive(_active);
    }
    
    public override void Activate(MACreatureInfo _creatureInfo, MABuilding _optionalOwner, bool _init, Vector3 _position, Vector3 _rotation, bool _addToCharacterLists = true)
    {
        base.Activate(_creatureInfo, _optionalOwner, _init, _position, _rotation, _addToCharacterLists);
        transform.position = _position.GroundPosition();
        m_isQuestCharacter = true;
        SetState(STATE.IDLE);
    }

    public static MAQuestGiver Create(MAWorkerInfo _questGiverInfo, Vector3 _pos)
    {
        if(_questGiverInfo == null) return null;
        var questGiver = MAWorker.Create(_questGiverInfo, _pos) as MAQuestGiver;
        
        if(questGiver == null)
            Debug.LogError("Trying to create a quest giver, but the allocated prefab does not have a quest giver script");
        
        return questGiver;
    }
    
    public void SetCapsuleCollidersEnable(bool _enabled)
    {
        CapsuleCollider[] colliders = GetComponentsInChildren<CapsuleCollider>();
        foreach (CapsuleCollider collider in colliders)
        {
            collider.enabled = _enabled;
        }
    }
}
