using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class MAQuestDogForeman : MAQuestDog
{
    enum m_pickupType
    {
        NotePart1,
        NotePart2,
        NotePart3,
        NotePart4,
        NotePart5,
        Lantern,
        Hardhat,
        Count
    }

    public MAQuestCutscene m_arriveCutscene, m_dialogueCutscene;
    public Transform m_dogFollowTransform;
    public MAQuestInteraction m_jumpCliff;
    public List <MAQuestInteraction> m_pickups;
    public List<GameObject> m_noteFragments;
    
    private float m_pickUpMessageDelay = 1f;   
    private Coroutine m_arriveCutsceneCoroutine, m_dialogueCutsceneCoroutine;
    private AnimationOverride m_animOverride = null;
    private bool m_arriveTimelineComplete = false;
    private bool m_arriveTimelineSkipped = true; //KW: default this to true so it skips to label on load
    private bool m_noteComplete = false;
    private bool m_notesActive = false;
    
    private bool m_pickupActive = false;

    private MAFlowCharacter m_joshuasWife, m_joshua, m_foremans<PERSON><PERSON><PERSON>, m_Executioner;

    private int NoteFragmentCount => ((int)m_pickupType.NotePart5 - (int)m_pickupType.NotePart1) + 1;
    private int NoteFragmentsCollected
    {
        get
        {
            int noteFragmentsCollected = 0;

            for (int i = (int)m_pickupType.NotePart1; i <= (int)m_pickupType.NotePart5; i++)
            {
                if (m_pickups[i].HasInteracted())
                {
                    noteFragmentsCollected++;
                }
            }

            return noteFragmentsCollected;
        }
    }

    private void InitReferences()
    {
        if (m_joshuasWife == null)
        {
            m_joshuasWife =
                NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == "ForemanWife") as MAFlowCharacter;

            if (m_joshuasWife != null)
            {
                m_lipSyncers["ForemanWife"] = m_joshuasWife;

                var ls = m_joshuasWife.SetLipSync(false);
                ls.m_deltaRot = -12.0f;
            }
        }
        
        if (m_joshua == null)
        {
            m_joshua =
                NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == "AccusedMale") as MAFlowCharacter;

            if (m_joshua != null)
            {
                m_lipSyncers["JoshuaRedgrave"] = m_joshua;

                var ls = m_joshua.SetLipSync(false);
                ls.m_deltaRot = -16.0f;
            }
        }
        
        if (m_foremansDaughter == null)
        {
            m_foremansDaughter =
                NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == "ForemanDaughter") as MAFlowCharacter;

            if (m_foremansDaughter != null)
            {
                m_lipSyncers["ForemanDaughter"] = m_foremansDaughter;

                var ls = m_foremansDaughter.SetLipSync(false);
                ls.m_deltaRot = -16.0f;
            }
        }
        
        // TODO: Add Jaw bone to executioner rig
        // if (m_Executioner == null)
        // {
        //     m_Executioner =
        //         NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == "Executioner") as MAFlowCharacter;
        //
        //     if (m_Executioner != null)
        //     {
        //         m_lipSyncers["Executioner"] = m_Executioner;
        //
        //         var ls = m_Executioner.SetLipSync(false);
        //         ls.m_deltaRot = -16.0f;
        //     }
        // }
    }

    public void OnPickupLantern()
    {
        m_pickups[(int)m_pickupType.Lantern].SetInteractive(false);
        m_pickups[(int)m_pickupType.Lantern].SetCollidersEnable(false);

        if(Camera.main != null)
        {
            var pickupHolder = Camera.main.transform.GetComponentInChildren<MAPickupHolder>();

            if(pickupHolder != null)
            {
                pickupHolder.Pickup(m_pickups[(int)m_pickupType.Lantern].gameObject, OnPickupLanternComplete, 90.0f, 2.0f);
                
                StartCoroutine(DelayedAction(() => 
                {
                    m_pickups[(int)m_pickupType.Lantern].SetInteracted(true);
                }, m_pickUpMessageDelay));

                return;
            }
        }
        OnPickupLanternComplete();
    }
    
    private void OnPickupLanternComplete()
    {
        m_pickups[(int)m_pickupType.Lantern].SetInteracted(true);
        m_pickups[(int)m_pickupType.Lantern].gameObject.SetActive(false);

        if (m_dog != null)
        {
            m_dog.AddItemsCollected();
        }
    }
    
    public void OnPickupHardhat()
    {
        m_pickups[(int)m_pickupType.Hardhat].SetInteractive(false);
        m_pickups[(int)m_pickupType.Hardhat].SetCollidersEnable(false);

        if(Camera.main != null)
        {
            var pickupHolder = Camera.main.transform.GetComponentInChildren<MAPickupHolder>();

            if(pickupHolder != null)
            {
                pickupHolder.Pickup(m_pickups[(int)m_pickupType.Hardhat].gameObject, OnPickupHardhatComplete,90.0f, 2.0f, false);

                EnableNotePickups();
                
                StartCoroutine(DelayedAction(() => 
                {
                    m_pickups[(int)m_pickupType.Hardhat].SetInteracted(true);
                }, m_pickUpMessageDelay));
                
                return;
            }
        }
        OnPickupHardhatComplete();
    }
    
    private void OnPickupHardhatComplete()
    {
        m_pickups[(int)m_pickupType.Hardhat].SetInteracted(true);
        m_pickups[(int)m_pickupType.Hardhat].gameObject.SetActive(false);

        if (m_dog != null)
        {
            m_dog.AddItemsCollected();
        }
    }
    
    public void OnPickupNote(int _partIndex)
    {
        if (m_pickupActive)
            return;
        m_pickupActive = true;
        
        m_pickups[_partIndex].SetInteractive(false);
        m_pickups[_partIndex].SetCollidersEnable(false);
        m_pickups[_partIndex].gameObject.SetActive(false);

        int noteFragmentsCollected = NoteFragmentsCollected;

        m_noteFragments[noteFragmentsCollected].transform.SetPositionAndRotation(
            m_pickups[_partIndex].transform.position,
            m_pickups[_partIndex].transform.rotation
        );
        m_noteFragments[noteFragmentsCollected].SetActive(true);

        if(Camera.main != null)
        {
            MAPickupHolder pickupHolder = Camera.main.transform.GetComponentInChildren<MAPickupHolder>();

            if(pickupHolder != null)
            {
                float rotateTime = 2.0f;
                if (noteFragmentsCollected >= NoteFragmentCount - 1)
                {
                    rotateTime = 8f;
                    
                    StartCoroutine(DelayedAction(() => 
                    {
                        m_noteComplete = true;
                    }, 1.5f));
                }
                pickupHolder.Pickup(m_noteFragments[noteFragmentsCollected].gameObject, () => OnPickupNoteComplete(_partIndex), 0.0f, rotateTime, true);
                FadeInCollectedNoteParts(pickupHolder);
                
                return;
            }
        }

        OnPickupNoteComplete(_partIndex);
    }
    
    private void OnPickupNoteComplete(int _partIndex)
    {      
        m_noteFragments[NoteFragmentsCollected].SetActive(false);
        m_pickups[_partIndex].SetInteracted(true);

        if (m_dog != null)
        {
            m_dog.AddItemsCollected();
        }

        m_pickupActive = false;
    }

    public void OnJumpCliff()
    {
        if (m_jumpCliff != null)
        {
            m_jumpCliff.SetInteracted(true);
            m_jumpCliff.SetInteractive(false);
        }
    }

    private void FadeInCollectedNoteParts(MAPickupHolder _pickupHolder)
    {
        for (int i = 0; i < NoteFragmentsCollected; i++)
        {
            int partIndex = i;
            _pickupHolder.FadeIn(m_noteFragments[i], () => OnFadeInNoteComplete(partIndex));
        }
    }

    private void OnFadeInNoteComplete(int _partIndex)
    {
        m_noteFragments[_partIndex].SetActive(false);
    }
    
    public override void QuestObjectiveActivate(string _objective)
    {
        if (_objective.Contains("CollectLantern"))
        {
            m_pickups[(int)m_pickupType.Lantern].SetInteractive(true, m_dog);
        }
        else if (_objective.Contains("CollectHardhat"))
        {
            m_pickups[(int)m_pickupType.Hardhat].SetInteractive(true, m_dog);
        }
        else if (_objective.Contains("CollectNote"))
        {
            for(int i = (int)m_pickupType.NotePart1; i <= (int)m_pickupType.NotePart5; i++)
            {
                m_pickups[i].SetInteractive(true, m_dog);
            }
        }
        else if (_objective.Contains("JumpCliff"))
        {
            m_jumpCliff.SetInteractive(true, m_dog);
        }
    }

    public override float QuestObjectiveValue(string _objective)
    {
        if (_objective.Contains("CollectLantern"))
        {
            return m_pickups[(int)m_pickupType.Lantern].HasInteracted() ? 0.0f : 1.0f;
        }
        if (_objective.Contains("CollectHardhat"))
        {
            return m_pickups[(int)m_pickupType.Hardhat].HasInteracted() ? 0.0f : 1.0f;
        }
        if (_objective.Contains("CollectNote"))
        {
            return NoteFragmentsCollected > 0 ? 0.0f : 1.0f;
        }
        if (_objective.Contains("CollectFragments"))
        {
            // int partsToCollect = NoteFragmentCount - NoteFragmentsCollected;
            return m_noteComplete ? 0.0f : 1.0f; ;
        }
        if (_objective.Contains("JumpCliff"))
        {
            return m_jumpCliff.HasInteracted() ? 0.0f : 1.0f;
        }
        if (_objective.Contains("ArriveTimelineComplete"))
        {
            return m_arriveTimelineComplete ? 0.0f : 1.0f;
        }
        return 1.0f;
    }

    public override void TriggerQuestEvent(string _event)
    {
        base.TriggerQuestEvent(_event);

        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("PlayCutscene"))
        {
            if (split[1].Contains("Arrive"))
            {
                if (m_arriveCutsceneCoroutine == null)
                {
                    m_arriveCutsceneCoroutine = StartCoroutine(Co_PlayArriveCutscene());
                }
            }
            else if(split[1].Contains("Dialogue"))
            {
                if (m_dialogueCutsceneCoroutine == null)
                {
                    m_dialogueCutsceneCoroutine = StartCoroutine(Co_PlayDialogueCutscene());
                }
            }
        }
        else if(split[0].Contains("ResumeCutscene"))
        {
            if (split[1].Contains("Arrive"))
            {
                m_arriveCutscene.Resume();
            }
        }
        else if (split[0].Contains("InitReferences"))
        {
            InitReferences();
        }
    }
    
    public override bool WaitForQuestEvent(string _event)
    {
        var split = _event.Split(';', '|', ':', '\n');
    
        if (split[0].Contains("QuestActive"))
        {
            return m_status == QuestStatus.Active;
        }
        else if (split[0].Contains("ArriveTimelineSkipped"))
        {
            return m_arriveTimelineSkipped;
        }

        return false;
    }

    private IEnumerator Co_PlayArriveCutscene()
    {
        m_arriveTimelineSkipped = false;

        yield return m_arriveCutscene.Co_Play(() =>
        {
            m_arriveTimelineSkipped = true;
        });

        m_arriveCutsceneCoroutine = null;
        m_arriveTimelineComplete = true;

        yield return null;
    }

    private IEnumerator Co_PlayDialogueCutscene()
    {
        yield return m_dialogueCutscene.Co_Play();

        m_dialogueCutsceneCoroutine = null;

        yield return null;
    }

    public void SetDogFollowingTransform(int _isFollowing)
    {
        if (m_dog != null)
        {
            if (_isFollowing > 0)
            {
                m_dog.transform.position = m_dogFollowTransform.position;
                m_dog.transform.rotation = m_dogFollowTransform.rotation;
                m_dog.SetStateFollowTransform(m_dogFollowTransform);
            }
            else
            {
                m_dog.SetState(NGMovingObject.STATE.IDLE);
            }
        }
    }

    public void StartJumpAnim()
    {
        if(m_animOverride == null)
        {
            // m_animOverride = AnimationOverride.PlayClip(m_dog.GetComponentInChildren<Animator>().gameObject, "DogJumpDownStart", null, "DogJumpDownLoop", OnLoopCB, "DogJumpDownEnd", null);
            m_animOverride = AnimationOverride.PlayClip(m_dog.GetComponentInChildren<Animator>().gameObject, "DogJumpDownStart", null);
            // AnimationOverride.InsertClip(m_dog.GetComponentInChildren<Animator>().gameObject, "DogJumpDownLoop", null);
            // void OnLoopCB(bool _interrupted)
            // {
            //     AudioClipManager.Me.PlaySoundOld("PlaySound_Worker_Land", m_dog.transform);
            // }
        }
    }

    public void EndJumpAnim()
    {
        if (m_animOverride != null)
        {
            m_animOverride = AnimationOverride.PlayClip(m_dog.GetComponentInChildren<Animator>().gameObject, "DogJumpDownEnd", null);
            // m_animOverride.Interrupt(false);
            m_animOverride = null;
        }
    }

    private void EnableNotePickups()
    {
        for (int i = (int)m_pickupType.NotePart1; i <= (int)m_pickupType.NotePart5; i++)
        {
            m_pickups[i].gameObject.SetActive(true);
        }

        m_notesActive = true;
    }

    public class SaveLoadQuestDogForemanContainer : SaveLoadQuestBaseContainer
    {
        public SaveLoadQuestDogForemanContainer() : base() { }
        public SaveLoadQuestDogForemanContainer(MAQuestBase _base) : base(_base) { }
        [Save] public ScentTrailSaveState m_scentTrailSaveState;
        [Save] public List<MAQuestInteraction.SaveState> m_interactionSaveStates;
        [Save] public int m_notesActive;
        [Save] public int m_disableRunningMusic;
    }
    public override SaveLoadQuestBaseContainer Save()
    {
        var saveContainer = new SaveLoadQuestDogForemanContainer(this);

        saveContainer.m_scentTrailSaveState = GetScentTrailSaveState();
        saveContainer.m_interactionSaveStates = GetInteractionSaveStates();
        saveContainer.m_notesActive = m_notesActive ? 1 : 0;
        saveContainer.m_disableRunningMusic = IsRunningMusicDisabled() ? 1 : 0;

        return saveContainer;
    }

    public override void Load(SaveLoadQuestBaseContainer _l)
    {
        base.Load(_l);

        var saveContainer = _l as SaveLoadQuestDogForemanContainer;
        if (saveContainer != null)
        {
            if (saveContainer.m_notesActive == 1)
            {
                EnableNotePickups();
            }
            SetScentTrailSaveState(saveContainer.m_scentTrailSaveState);
            SetInteractionSaveStates(saveContainer.m_interactionSaveStates);
            SetRunningMusicDisabled(saveContainer.m_disableRunningMusic == 1);
        }
    }

    private List<MAQuestInteraction.SaveState> GetInteractionSaveStates()
    {
        List<MAQuestInteraction.SaveState> interactionSaveStates = new List<MAQuestInteraction.SaveState>();

        for(int i = 0; i < (int)m_pickupType.Count; i++)
        {
            interactionSaveStates.Add(m_pickups[i].GetSaveState());
        }

        return interactionSaveStates;
    }

    private void SetInteractionSaveStates(List<MAQuestInteraction.SaveState> _interactionSaveStates)
    {
        if (_interactionSaveStates != null)
        {
            for (int i = 0; i < (int)m_pickupType.Count; i++)
            {
                if (i < _interactionSaveStates.Count)
                {
                    m_pickups[i].SetSaveState(_interactionSaveStates[i]);

                    if (m_pickups[i].HasInteracted())
                    {
                        m_pickups[i].gameObject.SetActive(false);
                        m_pickups[i].SetCollidersEnable(false);
                    }
                }
            }
        }
    }

    override public void SetQuestStatus(QuestStatus _status)
    {
        base.SetQuestStatus(_status);

        if (m_dog != null)
        {
            for (int i = 0; i < (int)m_pickupType.Count; i++)
            {
                if (m_pickups[i].IsInteractive())
                {
                    m_pickups[i].SetInteractive(true, m_dog);
                }
            }
        }
    }
}
