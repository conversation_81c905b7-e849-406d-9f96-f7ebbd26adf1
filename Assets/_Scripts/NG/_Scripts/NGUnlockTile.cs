using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
public class NGUnlockTile : NGDirectionCardBase
{
    // Update is called once per frame
    protected override void Activate(NGBusinessGift _gift, MAParserSection _maParserSection, INGDecisionCardHolder _fromHolder)
    {
        base.Activate(_gift, _maParserSection, _fromHolder);

        if (m_gift.m_spritePath.IsNullOrWhiteSpace() == false)
            m_image.sprite = m_gift.GetSprite;
    }

    public override NGCardInfoHolder GetDescriptionText()
    {
        return new NGCardInfoHolder(m_gift.m_giftTitle, m_gift.m_description, Cost, 1, "Block");
    }

    override public void GiveReward()
    {
        base.GiveReward();
        
        PayGiftCost();
        //NGTutorialManager.Me.cardUnlocks.Add(m_title.text);
        if (m_gift.m_power.IsNullOrWhiteSpace() == false)
        {
            if(MAParserSupport.TryParse(m_gift.m_power, out var notNeeded, $"From Gift[{m_gift.m_name}]") == false)
            {
                Debug.LogError($"Gift {m_gift.m_name} has an invalid power {m_gift.m_power}");
            }
        }
    }

}
