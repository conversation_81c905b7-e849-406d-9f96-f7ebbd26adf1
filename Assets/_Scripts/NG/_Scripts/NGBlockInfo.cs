using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;
using SaveContainers;
using System.IO;

[System.Serializable]
public class NGBlockInfo
{
    //public const string XLSName = ReactCSVImport.c_xlsNGBalance + "/NGKnack/NGBlocks";
    public enum BlockType
    {
        None = -1,
        Building,
        Product,
        Machine,
        Misc,
        Decorations,
        Packaging,
    }

    public enum RarityType
    {
        None,
        Common,
        Epic,
        Rare,
        Premium,
        Legendary,
    }
    public string id;
    public bool m_debugChanged;

    private BlockMarketInfo m_marketInfo;
    public BlockMarketInfo MarketInfo
    {
        get
        {
            if(m_marketInfo != null) return m_marketInfo;
            m_marketInfo = MAMarketForcesManager.Me.GetOrCreateBlockInfo(this);
            return m_marketInfo;
        }
    }
    
    public string m_prefabName;
    public string m_buildingPartType;
    [ScanField] public string m_nGProductInfo;
    [ScanField] public string m_tags;
    public string[] m_tagArray;
    public string m_rarity;
    public float m_price;
    public float m_sellingPrice;
    public float m_workerTimeToMake;
    public float m_numTapsToMake;
    public float m_materialCost;
    [ScanField] public string m_buildingMaterials;
    [ScanField] public string m_productMaterials;

    public float m_buildingRaise;
    public string m_extentType;
    
    public bool m_starterPack;
    public string m_prefabPath;
    public int m_drawer;
    public float m_drawerScale;
    public float m_tableScale;
    public int m_drawOrderPriority;
    public string m_displayName;
    public string m_description;
    public int m_usageCap;
    public float m_usesModifier;
    [ScanField] public string m_components;
    [ScanField] public string m_mADrawerInfos;
    public List<NGProductInfo> m_productInfos;
    public float m_defence;
    public float m_attack;
    public float m_beauty;
    public float m_nutrition;
    
    public bool m_isInvisible = false;
    public bool m_hideFromUser = false;
    public bool m_dontAllowDelete = false;

    public string PriceString => GlobalData.CurrencySymbol + Price.ToString("F2") + MarketInfo.BlockCostModifierText;
    public string SellingPriceString => GlobalData.CurrencySymbol + SellingPrice.ToString("F2") + MarketInfo.SalesPriceModifierText;
    public float Price => m_price * MarketInfo.m_currentBlockCostMultiplier;
    public float SellingPrice => m_sellingPrice * MarketInfo.m_currentSalesPriceMultiplier;

    public (string, string) GetAttackDetails() => ("Attack Damage", $"{MADesignGuage.GetNameValue(MADesignGuage.GaugeType.Attack, m_attack/1f)}");
    public (string, string) GetNurtitionalDetails() => ("Nutritional Value", $"{MADesignGuage.GetNameValue(MADesignGuage.GaugeType.Nutrition, m_nutrition/0.05f)}");
    public (string, string) GetDefenseDetails() => ("Defense", $"{MADesignGuage.GetNameValue(MADesignGuage.GaugeType.Defense, m_defence/50f)}");
    public (string, string) GetAsetheticDetails() => ("Aesthetic Value", $"{MADesignGuage.GetNameValue(MADesignGuage.GaugeType.Beauty, m_beauty/0.05f)}");
    
    public bool IsUnlocked()
    {
        if(m_starterPack == false && GameManager.IsUnlocked(m_prefabName) == false)
            return false;
            
        var drawerInfos = MADrawerInfo.GetInfos(m_mADrawerInfos);
        foreach (var drawerInfo in drawerInfos)
        {
            if(drawerInfo.IsUnlocked())
                return true;
        }
        return false;
    }

    public List<NGCarriableResource> GetProductMaterials(NGDesignInterface.DesignScoreInterface _dsi) => GetProductMaterials(_dsi?.ProductLine);
    public List<NGCarriableResource> GetProductMaterials(NGProductInfo _productLine)
    {
        var results = GetDefaultProductMaterials();
        _productLine?.ConvertToProductLineMaterials(results);
        return results;
    }
    
    public List<NGCarriableResource> GetDefaultProductMaterials()
    {
        var results = new List<NGCarriableResource>();
        if (string.IsNullOrEmpty(m_productMaterials))
            return results;
        foreach (var s in m_productMaterials.Split('|', ';', '\n'))
        {
            var resource = NGCarriableResource.GetInfo(s);
            if (resource != null)
                results.Add(resource);
        }
        return results;
    }

    public List<NGCarriableResource> BuildingMaterials {
        get
        {
            var results = new List<NGCarriableResource>();
            if(m_buildingMaterials != null)
            {
                foreach (var s in m_buildingMaterials.Split('|', ';', '\n'))
                {
                    var resource = NGCarriableResource.GetInfo(s);
                    if(resource != null)
                        results.Add(resource);
                }
            }
            return results;
        }}
    
    public List<MAComponentInfo> GetComponentClasses()
    {
        var result = new List<MAComponentInfo>();
        foreach (var c in m_components.Split(';', '|', '\n', ','))
        {
            if (MAComponentInfo.s_componentInfos.ContainsKey(c))
            {
                var component = MAComponentInfo.s_componentInfos[c];
                
                if (component != null)
                {
                    result.Add(component);
                }
            }
        }
        return result;
    }
    
    public List<NGProductInfo> ToProductInfo
    {
        get
        {
            var results = new List<NGProductInfo>();
            var split = m_nGProductInfo.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var s in split)
            {
                if (NGProductInfo.s_allProducts.ContainsKey(s))
                {
                    results.Add(NGProductInfo.s_allProducts[s]);
                }
            }
            return results;
        }
    }

    public List<string> TypeIDs
    {
        get
        {
            List<string> result = new List<string>();
            if (!string.IsNullOrEmpty(m_buildingPartType))
                result.AddRange(m_buildingPartType.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries));

            if (!string.IsNullOrEmpty(m_nGProductInfo))
                result.AddRange(m_nGProductInfo.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries));
        
            return result;
        }
       
    }

    public static string FloatToPercent(float _f) { return $"{_f * 100:n0}%"; }

    public BlockType Type
    {
        get
        {
            if (!m_nGProductInfo.IsNullOrWhiteSpace())
                return BlockType.Product;
            if(!m_buildingPartType.IsNullOrWhiteSpace())
                return BlockType.Building;
            return BlockType.None;
        }
    }

    public string Lines 
    {
        get
        {
            if (!string.IsNullOrEmpty(m_nGProductInfo))
                return m_nGProductInfo;
            if (!string.IsNullOrEmpty(m_buildingPartType))
                return m_buildingPartType;
            return null;
        }
    }
    
    public NGBalance.RarityType Rarity { get {
        Enum.TryParse(m_rarity, out NGBalance.RarityType result);
        return result;
    }}
    
    public float RarityPercent => RarityInfo.GetSettings(m_rarity);
    public static Dictionary<string, NGBlockInfo> s_allBlocks = new Dictionary<string, NGBlockInfo>();
    public static List<NGBlockInfo> s_blockInfos = new List<NGBlockInfo>();
    public static List<NGBlockInfo> GetList=> s_blockInfos;
    public string DebugDisplayName => m_prefabName;

    public static NGBlockInfo GetInfo(string _name)
    {
        if (s_allBlocks.ContainsKey(_name))
            return s_allBlocks[_name];
        return null;
    }
    
    public void RemoveComponentFromCount(Dictionary<MAComponentInfo,int> _componentCounts)
    {
        foreach(var c in GetComponentInfos())
        {
            _componentCounts[c]--;
        }
    }

    public static bool PostImportARecord(NGBlockInfo _what)
    {
        if (string.IsNullOrEmpty(_what.m_prefabPath))
        {
            Debug.LogError($"Houston we have a problem no prefab path has been set! Update NGKnack/NGBlockInfo {_what.m_prefabName}");
            return false;
        }

        bool hasCategoryLine = false;
        if (!string.IsNullOrEmpty(_what.m_buildingPartType))
        {
            hasCategoryLine = true;
        }

        if(!string.IsNullOrEmpty(_what.m_tags))
        {
            _what.m_tagArray = _what.m_tags.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
        }
        
        if (!string.IsNullOrEmpty(_what.m_nGProductInfo))
        {
            hasCategoryLine = true;
            _what.m_productInfos = _what.ToProductInfo;
        }
        
        if (!hasCategoryLine)
        {
            Debug.LogError($"We have a problem with NGKnack/NGBlockInfo no category has been assigned to block. Set either productInfo or buildingInfo{_what.m_prefabName}");
            return false;
        }

        if(_what.m_components.IsNullOrWhiteSpace())
            _what.m_components = "Aesthetic";
            
        if (MAComponentInfo.IsLoaded)
        {
            if (_what.m_components.IsNullOrWhiteSpace() == false)
            {
                foreach (var c in _what.m_components.Split('|', ';', '\n', ','))
                {
                    if (MAComponentInfo.s_componentInfos.ContainsKey(c) == false)
                    {
                        Debug.LogError($"{_what.m_prefabName} has unrecognised component of {c}");
                        continue;
                    }
                }
            }
        }
            
        return true;
    }

    public List<MAComponentInfo> GetComponentInfos()
    {
        var result = new List<MAComponentInfo>();
        foreach (var c in m_components.Split(';', '|', '\n', ','))
        {
            var comp = MAComponentInfo.GetInfo(c);
            if (comp != null)
            {
                result.Add(comp);
            }
        }

        return result;
    }
    public void AddBlockComponents(GameObject _block, bool _showBlockIcons = true)
    {
        _block.name = _block.name.Replace("(Clone)", "");
        bool hasAnyComponent = false;
        foreach (var c in m_components.Split(';', '|', '\n', ','))
        {
            hasAnyComponent |= AddComponent(_block, c);
        }
        
        if (_showBlockIcons)
            _block.AddComponent(System.Type.GetType("MAComponentIcons"));
    }
    
    private bool AddComponent(GameObject _block, string _componentInfoName)
    {
        if (MAComponentInfo.s_componentInfos.TryGetValue(_componentInfoName, out var component) == false)
            return false;
            
        if (component.m_class.IsNullOrWhiteSpace())
            return false;

        var addedComponent = _block.GetComponent(component.m_class);
        if (addedComponent == null)
        {
            addedComponent = _block.AddComponent(component.m_classType);
            if (addedComponent)
                _block.name += $">{_componentInfoName}";
        }

        if (addedComponent != null)
        {
            (addedComponent as BCBase).Setup(component);
            return true;
        }
        return false;
    }
    
    public static List<NGBlockInfo> LoadInfo()
    {
        s_blockInfos = NGKnack.ImportKnackInto<NGBlockInfo>(PostImportARecord);
        
        s_allBlocks.Clear();
        foreach (var b in s_blockInfos)
            if (s_allBlocks.ContainsKey(b.m_prefabName) == false)
                s_allBlocks.Add(b.m_prefabName, b);

        // send to ResearchLabController
        ResearchLabRewardsController.AddBlockInfos();
        
        // add to snap shot and block balance manager
        BlockBalanceManager.SetBlockData();
        
        AddBaseBlocks();
        
        return s_blockInfos;
    }

    private static void AddBaseBlock(string _id, string _path)
    {
        s_allBlocks[_id] = new NGBlockInfo
        {
            m_prefabPath = _path,
            m_prefabName = _id,
            m_components = "",
            m_productInfos = new(),
            m_mADrawerInfos = "",
            m_displayName = "",
            m_description = "",
        };
    }

    private static void AddBaseBlocks()
    {
        for (int z = 1; z <= 5; ++z)
            for (int x = 1; x <= 5; ++x)
                AddBaseBlock($"MABase{x}x{z}", $"_CurrentlyUsed/_MA_Base/MABase{x}x{z}");
    }

    public static Dictionary<RarityType, float> s_partRarity = new Dictionary<RarityType, float>()
    {
        {RarityType.None, 0f },
        {RarityType.Common, .04f },
        {RarityType.Rare, .08f },
        {RarityType.Epic, .12f },
        {RarityType.Legendary, .16f },
        {RarityType.Premium, .2f },
    };
}
