using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class BlockInfoPanelV2 : MonoBehaviour
{
    public TMP_Text m_title;
    public TMP_Text m_description;
    public Image m_image;
    public Transform m_lineHolder;
    public BlockInfoPanelLine m_linePrefab;

    private NGBlockInfo m_blockInfo;
    private PaintPotData m_paintInfo;
    private PatternData m_patternInfo;
    private StickerData m_stickerInfo;
    
    void AddTimeToMake()
    {
        var factory = DesignTableManager.Me.Building;
        int workers = 1;
        string line = "";
        /*if (factory && factory.Workers != null)
        {
            workers = (factory.Workers.Count > 0) ? factory.Workers.Count : workers;
            line = (factory.GetOverallWorkerSpeed() * m_blockInfo.m_workerTimeToMake).ToShortTimeString(_showFraction: true);
        }
        else*/
        {
            line = m_blockInfo.m_workerTimeToMake.ToShortTimeString(_showFraction: true);
        }

        var workersString = (workers > 1) ? $"{workers} Workers" : $"{workers} Worker";
        CreateLine($"{workers} Time To Make", line);
    }

    void AddMaterials(bool _isBuildingMode, NGDesignInterface.DesignScoreInterface _dsi)
    {
        var materials = _isBuildingMode ? m_blockInfo.BuildingMaterials : m_blockInfo.GetProductMaterials(_dsi);

        string matString = ""; 
        foreach (var mat in materials) 
            matString += $"{m_blockInfo.m_materialCost} x {mat.TextSprite} {mat.m_title} ";
        matString = matString.TrimEnd();
        CreateLine("Materials Required", matString);
    }

    void AddComponents()
    {
        var components = m_blockInfo.GetComponentClasses();
        //var requiredActionList = "";
        
        var componentsLine = "";
        var requires = "";
        
        foreach(var c in components)
        {
            //requiredActionList += MAComponentInfo.GetActionChoice(c);
            if(c.m_showInInfo == false) continue;
            
            componentsLine += (componentsLine.IsNullOrWhiteSpace() ? "" : "\n") + c.m_title;
            
            foreach(var required in c.m_requiredComponents)
            {
                requires += (requires.IsNullOrWhiteSpace() ? "" : "\n") + required.m_title;
            }
        }
        
        CreateLine("Features", componentsLine);
        CreateLine("Requires", requires);
        //CreateLine("Required By", requiredActionList);
    }

    void Activate(StickerData _info, bool _isBuildingMode, Sprite _icon, GameObject _owner)
    {
        AudioClipManager.Me.PlayUISound(_isBuildingMode ? "PlaySound_DesignModeBuildingShowInfo" : "PlaySound_DesignModeItemShowInfo");

        m_stickerInfo = _info;
        m_title.text = m_stickerInfo.m_name;
        m_description.text = m_stickerInfo.m_description;
        if (_icon != null)
            m_image.sprite = _icon;
        else
            m_image.transform.parent.gameObject.SetActive(false);
        m_lineHolder.DestroyChildren();
        
        CreateLine("Rarity", m_stickerInfo.m_rarity);
        CreateLine("Cost To Use", GlobalData.CurrencySymbol + m_stickerInfo.m_cost.ToString("F2"));
        CreateLine("Design Score Effect", $"+{m_stickerInfo.m_score.ToString("P2")}");
    }
    
    void Activate(PatternData _info, bool _isBuildingMode, Sprite _icon, GameObject _owner)
    {
        AudioClipManager.Me.PlayUISound(_isBuildingMode ? "PlaySound_DesignModeBuildingShowInfo" : "PlaySound_DesignModeItemShowInfo");

        m_patternInfo = _info;
        m_title.text = m_patternInfo.m_name;
        m_description.text = m_patternInfo.m_description;
        if (_icon != null)
            m_image.sprite = _icon;
        else
            m_image.transform.parent.gameObject.SetActive(false);
        m_lineHolder.DestroyChildren();
        
        CreateLine("Rarity", m_patternInfo.m_rarity);
        CreateLine("Cost To Use", GlobalData.CurrencySymbol + m_patternInfo.m_cost.ToString("F2"));
        CreateLine("Design Score Effect", ConvertMultiplierToString(m_patternInfo.m_partScoreMultiplier));
        CreateLine("Selling Price Effect", ConvertMultiplierToString(m_patternInfo.m_sellingPriceMultiplier));
    }
    
    void Activate(PaintPotData _info, bool _isBuildingMode, Sprite _icon, GameObject _owner)
    {
        AudioClipManager.Me.PlayUISound(_isBuildingMode ? "PlaySound_DesignModeBuildingShowInfo" : "PlaySound_DesignModeItemShowInfo");

        m_paintInfo = _info;
        m_title.text = m_paintInfo.m_name;
        m_description.text = m_paintInfo.m_description;
        if (_icon != null)
            m_image.sprite = _icon;
        else
            m_image.transform.parent.gameObject.SetActive(false);
        m_lineHolder.DestroyChildren();
        
        CreateLine("Rarity", m_paintInfo.m_rarity);
        CreateLine("Cost To Use", GlobalData.CurrencySymbol + m_paintInfo.m_cost.ToString("F2"));
        CreateLine("Design Score Effect", ConvertMultiplierToString(m_paintInfo.m_partScoreMultiplier));
        CreateLine("Selling Price Effect", ConvertMultiplierToString(m_paintInfo.m_sellingPriceMultiplier));
    }
    
    private string ConvertMultiplierToString(float _multiplier)
    {
        if(_multiplier == 1)
            return $"{(_multiplier-1).ToString("P2")}";
        if(_multiplier > 1)
            return $"+{(_multiplier - 1).ToString("P2")}";
        return $"<color=red>-{(1-_multiplier).ToString("P2")}</color>";
    }
    
    void Activate(NGBlockInfo _info, bool _isBuildingMode, Sprite _icon, GameObject _owner)
    {
        AudioClipManager.Me.PlayUISound(_isBuildingMode ? "PlaySound_DesignModeBuildingShowInfo" : "PlaySound_DesignModeItemShowInfo");
        var partArray = new DesignUtilities.PartDescription[1] { new DesignUtilities.PartDescription(){m_blockID = _info.m_prefabName}};
        var designInterface = NGDesignInterface.Get(partArray, DesignTableManager.Me.Order);

        m_blockInfo = _info;
        m_title.text = m_blockInfo.m_displayName;
        m_description.text = m_blockInfo.m_description;
        if (_icon != null)
            m_image.sprite = _icon;
        else
            m_image.transform.parent.gameObject.SetActive(false);
        m_lineHolder.DestroyChildren();
        
        if(_isBuildingMode == false)
            AddMaterials(_isBuildingMode, designInterface);
        
        if(!_isBuildingMode)
        {
            CreateLine("Cost To Drag Into Design", m_blockInfo.PriceString);
            CreateLine("Adds To Selling Price", m_blockInfo.SellingPriceString);
            CreateLine("Profit", GlobalData.CurrencySymbol + (m_blockInfo.SellingPrice-m_blockInfo.Price).ToString("F2"));
        }
        else
        {
            var price = designInterface.GetPriceOfBlock(m_blockInfo);
            CreateLine("Cost To Drag Into Design", GlobalData.CurrencySymbol + price.ToString("F2"));
        }
        
        CreateLine("Times Used In Design Before Penalty", m_blockInfo.m_usageCap.ToString());
        
        if(!_isBuildingMode)
            CreateLine("Difficulty To Make", $"{MADesignGuage.GetNameValue(MADesignGuage.GaugeType.DifficultyToMake, m_blockInfo.m_workerTimeToMake/60f)}");
        
        var nutrition = m_blockInfo.GetNurtitionalDetails();
        var attack = m_blockInfo.GetAttackDetails();
        var defense = m_blockInfo.GetDefenseDetails();
        var aesthetic = m_blockInfo.GetAsetheticDetails();
        
        CreateLine(nutrition.Item1, nutrition.Item2);
        CreateLine(attack.Item1, attack.Item2);
        CreateLine(defense.Item1, defense.Item2);
        CreateLine(aesthetic.Item1, aesthetic.Item2);
        
        if(_isBuildingMode)
            AddComponents();
#if UNITY_EDITOR
        //CreateLine("Debug", $"Bo={designInterface.Parts[0].BoredomPercent}, Pr={designInterface.SellingPrice:F4}, Sc={designInterface.TotalScore}");
#endif
    }

    public void ClickedClose()
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        DestroyMe();
    }

    void DestroyMe()
    {
        s_currentPanel = null;
        Destroy(gameObject);
    }

    BlockInfoPanelLine CreateLine(string _key, string _line)
    {
        return BlockInfoPanelLine.Create(m_linePrefab, m_lineHolder, _key, _line);
    }

    private static BlockInfoPanelV2 s_currentPanel = null;
    public static void DestroyPreviousInstance()
    {
        if (s_currentPanel == null) return;
        Destroy(s_currentPanel.gameObject);
        s_currentPanel = null;
    }

    public static BlockInfoPanelV2 Create(StickerData _info, bool _isBuildingMode, Sprite _icon, GameObject _owner)
    {
        DestroyPreviousInstance();
        var go = Instantiate(NGManager.Me.m_blockInfoPanelV2Prefab, DesignTableManager.Me.m_designTableUI);
        var bip = go.GetComponent<BlockInfoPanelV2>();
        bip.Activate(_info, _isBuildingMode, _icon, _owner);
        return bip;
    }
    public static BlockInfoPanelV2 Create(PatternData _info, bool _isBuildingMode, Sprite _icon, GameObject _owner)
    {
        DestroyPreviousInstance();
        var go = Instantiate(NGManager.Me.m_blockInfoPanelV2Prefab, DesignTableManager.Me.m_designTableUI);
        var bip = go.GetComponent<BlockInfoPanelV2>();
        bip.Activate(_info, _isBuildingMode, _icon, _owner);
        return bip;
    }
    public static BlockInfoPanelV2 Create(PaintPotData _info, bool _isBuildingMode, Sprite _icon, GameObject _owner)
    {
        DestroyPreviousInstance();
        var go = Instantiate(NGManager.Me.m_blockInfoPanelV2Prefab, DesignTableManager.Me.m_designTableUI);
        var bip = go.GetComponent<BlockInfoPanelV2>();
        bip.Activate(_info, _isBuildingMode, _icon, _owner);
        return bip;
    }
    public static BlockInfoPanelV2 Create(NGBlockInfo _info, bool _isBuildingMode, Sprite _icon, GameObject _owner)
    {
        DestroyPreviousInstance();
        var go = Instantiate(NGManager.Me.m_blockInfoPanelV2Prefab, DesignTableManager.Me.m_designTableUI);
        var bip = go.GetComponent<BlockInfoPanelV2>();
        bip.Activate(_info, _isBuildingMode, _icon, _owner);
        return bip;
    }

}
