using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System;

public class NGBaseInfoGUI : MAGUIBase
{
    static public NGBaseInfoGUI s_infoShowing;
    public TMP_Text m_title;
    public Transform m_componentHolder;
    public Toggle m_pinToggle;
    
    virtual protected string AudioHook_Close => "PlaySound_BuildingContextMenu_InfoScreenClose";


    static public void ReplaceShowing(NGBaseInfoGUI next = null)
    {
        if(s_infoShowing != null)
            s_infoShowing.DestroyMe();
        s_infoShowing = next;
    }
    public void ClickedClose()
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        if (GameManager.Me.IsOKToPlayUISound())
            AudioClipManager.Me.PlaySound(AudioHook_Close, GameManager.Me.transform);
        DestroyMe();
    }

    virtual public void ClickedEditName()
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
    }

    virtual public void GotNewName(string _newName)
    {
        m_title.text = _newName;
    }

    public void ClickedPinButton()
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        NGManager.Me.m_pinInfo = !NGManager.Me.m_pinInfo;
    }
    public void DestroyMe()
    {
        if (this == s_infoShowing)
            s_infoShowing = null;
        NGManager.Me.m_pinInfo = true;
        Destroy(gameObject);
    }

    protected float AddComponent(string _name, bool _addToScrollRect = true)
    {
        if(m_componentHolder == null) return 0f;
        Transform parent = _addToScrollRect ? m_componentHolder : m_componentHolder.GetComponentInParent<ScrollRect>().transform.parent.parent;
        var component = CreateComponent(_name, parent);
        if (component == null)
            return 0f;
        if (!_addToScrollRect) component.SetSiblingIndex(component.transform.GetSiblingIndex() - 1);
        var rt = component.GetComponent<RectTransform>();
        return rt.GetHeight();
    }

    virtual protected Transform CreateComponent(string _name, Transform _parent)
    {
        return null;
    }
    virtual protected float Activate(string _title)
    {
        if (GameManager.Me.IsOKToPlayUISound())
            AudioClipManager.Me.PlaySoundOld("PlaySound_Window_Open", GameManager.Me.transform);
//        ReplaceShowing(this);
        m_pinToggle.isOn = !NGManager.Me.m_pinInfo;
//        m_componentHolder.DestroyChildren();
        m_title.text = _title;
        //MABuildingInfo.Create(m_buil)
        return AddComponent("Info", true);
    }
}
