using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ReactPickupMulti : ReactPickupPersistent
{
    public string m_carriableName;
    void Activate(System.Action<GameObject> _cb = null)
    {
        var info = NGCarriableResource.GetInfo(m_carriableName);
        Initialise(null, info , 99999);
        if(_cb != null) _cb(this.gameObject);
    }
    override public void HasCollidedWith(NGCommanderBase _what) 
    {
        Destroy(gameObject);
    }

    public string GetProductStock()
    {
        var results = "";
        /*foreach (var s in m_spawnedFrom.InputsAre.Items)
        {
            if(s.m_stock > 0)
                results += $"{s.m_resource.m_title} = {s.m_stock}\n";
        }*/

        results = results.TrimEnd('\n');
        return results;
    }

    override protected void OnTriggerEnter(Collider collision)
    {
        base.OnTriggerEnter(collision);
    }

    virtual protected void InternalCollisionEnter(GameObject collision)
    {
        InternalCollisionEnter(collision);
    }
    
    override public GameObject GetBestTarget(int _inputId, GameObject _obj, Vector3 _pos, out SpecialHandlingAction _action) {
        return InputUtilities.NGFindBestCollider(_inputId, _obj.GetComponentInChildren<NGMovingObject>(), out _action);
    }

    public static ReactPickupMulti Create(System.Action<GameObject> _cb = null)
    {
        var go = Instantiate(NGCarriableResource.GetPrefabForResource(NGCarriableResource.GetInfo("RawResourceAny")), GlobalData.Me.m_pickupsHolder);

        var rpm = go.GetComponent<ReactPickupMulti>();
        rpm.Activate(_cb);
        return rpm;
    }
    
}
