using System;
using UnityEngine;
using TMPro;

public class WallRepairingInfoGUI : NGBaseInfoGUI
{
    public TMP_Text m_repairStatus = null;
    public TMP_Text m_woodRequired = null;
    public TMP_Text m_health = null;
    public TMP_Text m_pauseButtonText;
    
    private PathBreak pathBreak = null;

    public static WallRepairingInfoGUI Create(PathBreak pb)
    {
        var wriGUI = Instantiate(NGManager.Me.m_WallRepairingInfoGUIPrefab, NGManager.Me.m_centreScreenHolder);
        wriGUI.Setup(pb);

        return wriGUI;
    }

    public void OnDestroy()
    {
        if (pathBreak != null)
        {
            pathBreak.DidRepair -= OnBreakRepaired;
            pathBreak.DidDestroy -= OnBreakDestroied;
        }
    }

    protected override Transform CreateComponent(string _name, Transform _parent)
    {
        return null;
    }

    private void Setup(PathBreak pb)
    {
        pathBreak = pb;
        pathBreak.DidRepair += OnBreakRepaired;
        pathBreak.DidDestroy += OnBreakDestroied;

        UpdateResourcesLabel();
        
        Activate("Damaged Wall");
    }

    public void OnPauseButtonClicked()
    {
        if(pathBreak.RepairOrder == 0)
            pathBreak.ResumeRepair();
        else
            pathBreak.StopRepair();
        ClickedClose();
    }
    
    public void OnRepairFirstButtonClicked()
    {
        pathBreak?.MakePriority();
        ClickedClose();
    }

    private void UpdateResourcesLabel()
    {
        float total, required;
        pathBreak.GetTotalAndRequiredResources(out total, out required);
        int totalInt = Mathf.FloorToInt(total);
        int requiredInt = Mathf.FloorToInt(required);
        if (required > 0f && required < 1f)
            requiredInt = 1;
        m_woodRequired.text = String.Format("{0}", requiredInt);
        m_health.text = $"{pathBreak.RepairLevel:P0}";
        m_repairStatus.text = pathBreak.Status;
        
        m_pauseButtonText.text = pathBreak.RepairOrder == 0 ? "Resume" : "Pause"; 
    }

    public void OnBreakRepaired(PathBreak pb)
    {
        ClickedClose();
    }

    public void OnBreakDestroied(PathBreak pb)
    {
        ClickedClose();
    }
}
