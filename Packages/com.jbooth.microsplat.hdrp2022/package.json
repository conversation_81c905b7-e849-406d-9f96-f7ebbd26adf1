{"name": "com.jbooth.microsplat.hdrp2022", "displayName": "MicroSplat-CoreHD2022", "version": "3.9.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jdbtechservices.com/"}, "unity": "2019.4", "samples": [{"displayName": "HDRP2022 Example", "description": "Example of HDRP2022", "path": "Samples~/HDRP2022"}], "dependencies": {"com.jbooth.microsplat.core": "3.9.0"}, "description": "The HDRP2022 module for MicroSplat allows MicroSplat to compile it's shaders for HDRP 14.x", "keywords": ["MicroSplat", "Terrain", "Shader"], "documentationUrl": "https://docs.google.com/document/d/1ySro4sgvVBEO6LFoGiZDZbttTzA44PZwQjPR8rTxCC4/edit?usp=sharing"}