{"name": "com.jbooth.microsplat.urp2022", "displayName": "MicroSplat-CoreURP2022", "version": "3.9.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jdbtechservices.com/"}, "unity": "2022.2", "samples": [{"displayName": "URP2022 Example", "description": "Example of URP2022", "path": "Samples~/URP2022"}], "dependencies": {"com.jbooth.microsplat.core": "3.9.0"}, "description": "The URP2022 module for MicroSplat allows MicroSplat to compile it's shaders for URP 14.x", "keywords": ["MicroSplat", "Terrain", "Shader", "URP"], "documentationUrl": "https://docs.google.com/document/d/1i29Tp5WcsFcIHCVgWV4rSnjgpw1kyENaVSSkHhgu0ro/edit?usp=sharing"}